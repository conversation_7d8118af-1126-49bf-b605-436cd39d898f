import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
// import commonjs from 'vite-plugin-commonjs' // vite构建commonjs
import tailwindcss from "tailwindcss";
// 该插件在处理动态class设置时(:class="['class1', {'class2': boolean}]")小程序开发工具预览时编译会报错:预览 Error: 1:5101:unexpected character `\`
// import uniTailwind from '@uni-helper/vite-plugin-uni-tailwind';
import { UnifiedViteWeappTailwindcssPlugin as uvwt } from 'weapp-tailwindcss/vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    // commonjs(),
    uni(),
    // uniTailwind(),
    uvwt(),
  ],
  css: {
    postcss: {
      plugins: [
        tailwindcss(),
        // tachyong使用配置
        // require('postcss-custom-properties')({
        //   // 将所有 CSS 变量展开为具体的值
        //   preserve: false,
        //   // 你也可以设置其他选项来更细粒度地控制如何替换变量
        // }),
      ],
    },
  },
})
