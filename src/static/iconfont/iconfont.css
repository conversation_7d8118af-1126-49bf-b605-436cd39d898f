@font-face {
  font-family: 'iconfont'; /* Project id  */
  src: url('iconfont.ttf?t=1729157828770') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconfont-share:before {
  content: '\ea87';
}

.iconfont-users:before {
  content: '\e61e';
}

.iconfont-send:before {
  content: '\e771';
}

.iconfont-keyboard:before {
  content: '\e696';
}

.iconfont-keyboard2:before {
  content: '\e8e7';
}

.iconfont-send2:before {
  content: '\e60b';
}

.iconfont-voice:before {
  content: '\ea70';
}

.iconfont-voice2:before {
  content: '\eace';
}
