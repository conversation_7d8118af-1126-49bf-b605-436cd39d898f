import { createSSRApp } from 'vue'
import App from './App.vue'
import * as <PERSON>nia from 'pinia'
import { createUnistorage } from 'pinia-plugin-unistorage'
import uviewPlus, { setConfig } from 'uview-plus'
// import './assets/tachyons-custom.css';
// import './assets/tailwind.css'; // App.vue

export function createApp() {
  const app = createSSRApp(App)

  const store = Pinia.createPinia()
  store.use(createUnistorage())
  app.use(store)

  app.use(uviewPlus)

  return {
    app,
    Pinia,
  }
}
