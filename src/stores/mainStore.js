import { ref, computed, reactive } from 'vue'
import { defineStore } from 'pinia'

export const useStore = defineStore(
  'main',
  () => {
    const userinfo = reactive({})
    const speechInteraction = reactive({})
    // const doubleCount = computed(() => count.value * 2)
    function updateUserinfo(payload) {
      Object.assign(userinfo, { ...payload })
    }

    return { userinfo, updateUserinfo, speechInteraction }
  },
  {
    // unistorage: true, // 开启后对 state 的数据读写都将持久化
    unistorage: {
      key: '__XQLJ_STORES', // 本地存储键值
      paths: ['userinfo'],
    },
  }
)
