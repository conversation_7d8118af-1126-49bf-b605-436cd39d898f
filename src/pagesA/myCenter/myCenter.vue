<template>
  <view class="flex flex-col text-gray-800 min-h-screen">
    <up-navbar title="我的" :border="false" placeholder bgColor="transparent"
      :titleStyle="{ color: '#fff' }" :autoBack="true"  leftIconColor="#fff">
      <!-- <template #left></template> -->
    </up-navbar>
    <view class="flex flex-col items-center pt-3 pb-6">
      <button
        class="w-[200rpx] h-[200rpx] overflow-hidden px-0 rounded-full after:content-none mb-3"
        open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
        <image class="w-[200rpx]"
          :src="userInfo.avatarUrl || '../../static/media/avatar.jpg'"
          mode="widthFix">
        </image>
      </button>
      <view
        class="text-center truncate max-w-32 mb-2.5 text-lg leading-6 font-semibold"
        @touchstart.stop="handleTouchInput">
        <input v-if="focused" class="bg-white h-6" type="nickname"
          v-model.trim="userInfo.nickname" placeholder="输入昵称" :maxlength="8"
          @blur="handleGetNickname" :focus="focused" />
        <text v-else>{{ userInfo.nickname }}</text>
      </view>
      <view
        class="flex items-center bg-white px-2 py-1 rounded border border-sky-950 text-sm">
        <image class='w-5 h-5 mr-2' src="../../static/media/ID.png"
          mode="widthFix" />
        <text>{{ userInfo.mobile?.toString().replace(/(\d{3})\d*(\d{4})/,
          `$1****$2`) }}</text>
      </view>
    </view>
    <view class="p-2.5 bg-white flex-1 text-sm">
      <view class="flex items-center px-5 py-3.5" @click="contactUs">
        <up-icon class="mr-2.5" name="kefu-ermai" size="16"
          color="#333"></up-icon>
        <view class="flex-1">联系客服</view>
      </view>
      <!-- <view class="menuGroupItem"
        @click="navigateTo({ url: '/pagesA/feedbacks/feedbacks' })">
        <view class="navigation">
          <image class="icon" src="../../static//media/query.png"
            mode="widthFix" />
          <text>意见反馈</text>
        </view>
        <image class="arrowRight" src="../../static/media/arrowRight.png"
          mode="widthFix" />
      </view> -->
      <view class="flex items-center px-5 py-3.5">
        <up-icon class="mr-2.5" name="order" size="17" color="#333"></up-icon>
        <view class="flex-1">版本号</view>
        <text class="text-[#6080A6]">{{ accountInfo.version ||
          accountInfo.envVersion
        }}</text>
      </view>
    </view>
    <!-- <up-popup :show="userPopupShow" :round="16" :safeAreaInsetBottom="false"
      closeable closeOnClickOverlay @close="handleClose">
      <view class="h-80">
        <GetAvatarNickName :data="userInfo" @cancel="cancelAvatarNickNamePopup"
          @save="saveAvatarNickname">
        </GetAvatarNickName>
      </view>
    </up-popup> -->
  </view>
</template>
<script setup>
  import { nextTick, ref, watch } from 'vue'
  import { onShow, onLoad } from '@dcloudio/uni-app'
  import { getUserInfo, profileEdit, getCustomer } from '@/api'
  import { useStore } from '@/stores/mainStore'
  // import GetAvatarNickName from '@/components/GetAvatarNickName.vue'
  import { wxAppid, baseURL } from '@/assets/config'
  const app = getApp()
  const store = useStore()
  const userInfo = ref({})
  const contactPhone = ref('')
  const accountInfo = wx.getAccountInfoSync().miniProgram
  const userPopupShow = ref(false) // 用户信息编辑弹窗
  let prevNickname = ''
  const focused = ref(false)

  onShow(() => {
    uni.hideHomeButton()
  })

  onLoad((option) => {
    app.globalData.loginPromise.then(() => {
      console.log('%c登录成功', 'color:green;')
      handleGetUserInfo()
      handleGetCustomer()
    })
  })

  const handleGetUserInfo = () => {
    getUserInfo()
      .then((res) => {
        console.log('获取用户信息成功', res.data)
        Object.assign(userInfo.value, res.data)
      })
      .catch((err) => {
        console.log('获取用户信息失败', err)
        uni.showToast({
          icon: 'error',
          title: '获取用户信息失败',
        })
      })
  }

  const handleTouchInput = () => {
    // console.log('handleTouchInput')
    // focused.value = false // 必须否则首次输入后输入框无法再获得焦点
    if (uni.requirePrivacyAuthorize) {
      uni.requirePrivacyAuthorize({
        success: (res) => {
          console.log('用户同意了隐私协议 或 无需用户同意隐私协议')
          // 用户同意隐私协议后给昵称input聚焦
          focused.value = true
        },
        fail: (res) => {
          console.log('用户拒绝了隐私协议')
        },
      })
    } else {
      focused.value = true
    }
    prevNickname = userInfo.value.nickname
  }

  const handleGetCustomer = () => {
    getCustomer()
      .then((res) => {
        contactPhone.value = res.data
      })
      .catch((err) => {
        uni.showToast({
          icon: 'error',
          title: '获取信息失败',
        })
        console.log(err.errMsg)
        console.log('获取客服信息失败:', err)
      })
  }
  const contactUs = async () => {
    // return
    // if (!contactPhone) {
    //   uni.showLoading({
    //     mask: true,
    //   })
    //   const response = await getCustomer().catch(err => {
    //     uni.showToast({
    //       icon: 'error',
    //       title: '获取信息失败',
    //     })
    //     console.log(err.errMsg)
    //     console.log('获取客服信息失败:', err)
    //   })
    //   uni.hideLoading()
    //   console.log(response);

    //   contactPhone = response.data?.phone
    // }
    if (contactPhone.value) {
      uni.makePhoneCall({
        phoneNumber: contactPhone.value,
        fail: (err) => {
          if (err.errMsg != 'makePhoneCall:fail cancel') {
            uni.showToast({
              icon: 'error',
              title: '获取信息失败',
            })
          }
          console.log('拨打客服电话失败:', err)
        },
      })
    }
  }
  // const navigateTo = (obj) => {
  //   uni.navigateTo(obj)
  // }

  // const cancelAvatarNickNamePopup = () => {
  //   userPopupShow.value = false
  // }

  // const saveAvatarNickname = (userinfo) => {
  //   // uni.showLoading()
  //   if (userinfo.nickName) {
  //   }
  // }

  // 获取头像
  const onChooseAvatar = (e) => {
    uni.uploadFile({
      url: baseURL + '/app/user/avatar/upload',
      filePath: e.detail.avatarUrl,
      name: 'file',
      header: {
        Issuer: wxAppid,
        Authorization: store.userinfo?.token,
        _R_: 'XUC',
      },
      success(res) {
        let result = JSON.parse(res.data)
        userPopupShow.value = false
        if (result.code === '0') {
          handleGetUserInfo()
          uni.showToast({
            icon: 'none',
            title: '修改成功',
          })
        } else {
          uni.showToast({
            icon: 'error',
            title: '头像上传失败',
          })
        }
        // uni.hideLoading()
      },
      fail: (err) => {
        userPopupShow.value = false
        console.log('头像上传错误:', err)
        uni.showToast({
          icon: 'error',
          title: '头像上传失败',
        })
      },
    })
  }

  // 获取昵称
  const handleGetNickname = (e) => {
    const nickname = e.detail.value

    if (prevNickname && nickname && nickname !== prevNickname) {
      profileEdit({
        data: {
          nickname,
        },
        header: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      })
        .then(() => {
          // handleGetUserInfo()
          uni.showToast({
            title: '修改成功',
          })
        })
        .catch((err) => {
          console.log('修改用户昵称失败', err)
          uni.showToast({
            icon: 'error',
            title: '昵称修改失败',
          })
        })
    }

    nextTick(() => {
      focused.value = false
    })
  }
</script>
