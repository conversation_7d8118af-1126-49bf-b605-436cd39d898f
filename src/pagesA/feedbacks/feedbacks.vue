<template>
	<view class="feedback">
		<view class="field-item">
			<view class="mb-2">问题分类</view>
			<up-subsection :list="questiontypes" mode="subsection" activeColor="#1A84FE"
				inactiveColor="#fff" :current="0" @change="handleSelecte"></up-subsection>
		</view>
		<view class="field-item">
			<view class="mb-2">详情描述</view>
			<textarea class="field-control" :adjust-position="false" rows="10"
				placeholder="请描述你的意见、建议或举报内容" v-model.trim="formData.text"></textarea>
		</view>
		<!-- <view class="field-item">
			<view class="mb-2">上传图片或截图</view>
			<view>
				<up-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic"
					name="1" multiple :maxCount="2"></up-upload>
			</view>
		</view> -->
		<view class="field-item">
			<view class="mb-2">联系方式</view>
			<input class="field-control" :adjust-position="false" type="text"
				placeholder="请填写您的联系方式" v-model.trim="formData.phone" />
		</view>
		<view class="d-flex align-items-center mb-8">
			<view class="flex-1"> 联系我们：zam19937749541 </view>
			<up-button class="w-25" type="primary" shape="circle" size="small" text="复制"
				plain @click="copyTextHandle('zam19937749541')"></up-button>
		</view>
		<view>
			<up-button type="primary" shape="circle" text="提交"
				@click="handleSubmit"></up-button>
		</view>
	</view>
</template>

<script setup>
	import { saveFeedbacks } from '@/api'
	// import { isPhoneNumber } from '@/utils/regExpTools.js'
	// import { uploadimgorvideo } from '@/components/uploadimgorvideo/uploadimgorvideo.vue'
	// import { uploadImg2 } from '@/utils/onlinetools.js'
	// // 引入公共基本数据(头部导航栏尺寸、底部tabBar栏尺寸、是否为ios系统)：
	// import { getSameDataMixin } from '@/utils/sameDataMixin.js'
	// // 引入: initDataBaseMixin基本数据初始化方法（headerHeight, headerWidth, tabBarHeight, isIos等变量的初识）、refreshIsLightOrDarkByTime朝夕模式自动刷新
	// import { initDataBaseMixin } from '@/utils/sameMethodsMixin.js'
	// // 导入本地工具
	// import { copyStringHandle } from '@/utils/offlineTools.js'
	// // 导入vuex中状态遍历方法：
	// import { mapState } from 'vuex'
	import { reactive } from 'vue'

	const questiontypes = [
		{ name: '产品建议' },
		{ name: '功能BUG' },
		{ name: '改进方向' },
		{ name: '其他' },
	]
	const formData = reactive({
		type: 0,
		text: '',
		// imgUrl: '',
		phone: '',
		// reply: '',
		// userId: 0,
		// states: 0,
	})
	// const fileList = ref([])

	const handleSelecte = (index) => {
		formData.type = index
	}

	// 新增图片
	// const afterRead = async (event) => {
	// 	// 当设置 mutiple 为 true 时, file 为数组格式, 否则为对象格式
	// 	let lists = [].concat(event.file);
	// 	let fileListLen = fileList.value.length;
	// 	lists.map((item) => {
	// 		fileList.value.push({
	// 			...item,
	// 			status: 'uploading',
	// 			message: '上传中',
	// 		});
	// 	});
	// 	for (let i = 0; i < lists.length; i++) {
	// 		const result = await uploadFilePromise(lists[i].url);
	// 		let item = fileList.value[fileListLen];
	// 		fileList.value.splice(fileListLen, 1, {
	// 			...item,
	// 			status: 'success',
	// 			message: '',
	// 			url: result,
	// 		});
	// 		fileListLen++;
	// 	}
	// };

	// 删除图片
	// const deletePic = (event) => {
	// 	fileList.value.splice(event.index, 1);
	// };

	// 提交表单
	const handleSubmit = async () => {
		if (!formData.text) {
			uni.showToast({
				title: '请填写描述内容',
				icon: 'none',
			})
			return
		}

		if (!formData.phone) {
			uni.showToast({
				title: '请填写联系方式',
				icon: 'none',
			})
			return
		}

		// 校验手机号是否正确
		if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
			uni.showToast({
				title: '请检查联系方式是否填写正确',
				icon: 'none',
				duration: 2000,
			})
			return
		}

		uni.showLoading({
			mask: true,
		})

		saveFeedbacks({
			data: {
				...formData,
				// imgUrl: fileList.value[0].url,
			},
		})
			.then(res => {
				uni.showToast({
					title: '提交成功',
					mask: true,
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 1200)
			})
			.catch(err => {
				console.log('意见反馈提交失败:', err)
				uni.showToast({
					icon: 'error',
					title: '反馈提交失败',
				})
			})
	}

</script>

<style lang="scss" scoped>
	.feedback {
		padding: 40rpx;
		color: #fff;
		font-size: 28rpx;

		.field-item {
			margin-bottom: 32rpx;
		}

		.field-control {
			background-color: #383838;
			width: auto;
			border-radius: 16rpx;
			padding: 20rpx 24rpx;
		}

		.head_top {
			width: 100%;
		}

		.issue_classify {
			.issue_classify_title {
				color: #e5e5e5;
				font-size: 36rpx;
				font-weight: 500;
				margin-bottom: 24rpx;
			}

			.issue_classify_content {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.issue_classify_item {
					padding: 10rpx 30rpx;
					color: #a6a6a6;
					border: 2rpx solid #a6a6a6;
					border-radius: 30rpx;
					margin-right: 40rpx;
					margin-bottom: 37rpx;
					font-size: 26rpx;
					font-weight: 500;
				}

				.active {
					background-color: #b5b9df;
					border: 2rpx solid #5a6cf4;
					color: #5a6af6;
				}
			}
		}

		.text_area_box {
			width: 100%;
			font-size: 32rpx;
			color: #fff;
			box-sizing: border-box;

			.text_area_box_title {
				color: #e5e5e5;
				font-weight: 500;
				font-size: 36rpx;
				margin-bottom: 24rpx;
			}

			textarea {
				width: 100%;
				height: 280rpx;
				background-color: #383838;
				padding: 20rpx 39rpx;
				border-radius: 20rpx;
				box-sizing: border-box;
			}
		}

		.upload_main_box {
			width: 100%;
			height: 260rpx;
			box-sizing: border-box;
			margin-top: 40rpx;
			display: flex;
			flex-direction: column;

			.upload_title {
				height: 60rpx;
				font-size: 36rpx;
				color: #fff;
			}

			.upload_box {
				flex: 1;
				width: 100%;

				::v-deep .imgvideoitem {
					width: auto !important;
					height: auto !important;

					image {
						width: 140rpx !important;
						height: 140rpx !important;
					}
				}
			}
		}

		.tel {
			width: 100%;
			box-sizing: border-box;
			margin-top: 12rpx;
			font-size: 32rpx;
			color: #fff;

			.tel_title {
				font-size: 36rpx;
				font-weight: 500;
				margin-bottom: 24rpx;
			}

			.tel_input {
				background-color: #383838;
				border-radius: 20rpx;
				padding: 18rpx 11rpx;
			}
		}

		.contactUs {
			margin-top: 46rpx;
			color: #e5e5e5;
			font-size: 36rpx;
			font-weight: 500;
			display: flex;
			justify-content: space-between;

			.contactUs_content {
				display: flex;
				align-items: baseline;

				.contactUs_title {
					// font-size: 36rpx;
					// font-weight: 500;
				}

				.txt {
					font-size: 30rpx;
					margin-left: 16rpx;
				}
			}
		}

		.copy {
			font-size: 26rpx;
			color: #a6a6a6;
			border-radius: 30px;
			border: 2rpx solid #a6a6a6;
			padding: 9rpx 34rpx;
		}

		.bottom_button {
			padding-bottom: 60rpx;
			width: 100%;
			height: 200rpx;
			display: flex;
			justify-content: center;
			align-items: flex-end;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 702rpx;
				height: 80rpx;
				color: #5a6af6;
				border-radius: 40rpx;
				font-size: 32rpx;
				color: #fff;
			}
		}
	}
</style>
