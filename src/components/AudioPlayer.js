// 调用stop后,需要调用start才能重新调用addAudio
class AudioPlayer {
  #innerAudioContext = null
  constructor() {
    // this.#innerAudioContext = null
    this.audioQueue = [] // 播放队列
    this.playing = false // 当前是否正在播放
    this.stopped = false // 标记本轮次播放已停止
    this.onPlayCallback = null
    this.onStopCallback = null
  }

  // 播放下一个音频
  #playNext() {
    if (this.audioQueue.length === 0) {
      this.playing = false
      return
    }

    // 获取队列中的第一个音频
    this.#destroy()
    this.#innerAudioContext = uni.createInnerAudioContext()
    this.#innerAudioContext.onPlay(() => {
      console.log('Audio start.')
      this.onPlayCallback && this.onPlayCallback()
    })
    this.#innerAudioContext.onEnded(() => {
      console.log('Audio stop:', this.audioQueue)
      this.playing = false
      this.onStopCallback && this.onStopCallback([...this.audioQueue])
      this.#playNext()
    })
    this.#innerAudioContext.onError((err) => {
      console.log('Audio error:', err)
      uni.showToast({
        title: '播放失败',
        icon: 'error',
        mask: true,
      })

      this.stop()
      // 播放错误也可以继续播放
      // this.#playNext()
    })

    this.#innerAudioContext.src = this.audioQueue.shift()
    this.#innerAudioContext.play()
    // 调用播放到触发onPlay开始播放会因资源等原因有一定间隔,这里马上赋值,避免播放状态判断错误
    this.playing = true
  }

  // 添加音频到队列
  addAudio(src) {
    if (this.stopped) {
      console.error(
        'Cannot add audio. The player is stopped. Call `start` to enable adding audio.'
      )
      return
    }
    if (!src) {
      console.error('Invalid audio URL')
      return
    }
    this.audioQueue.push(src)
    console.log('Audio added:', this.audioQueue)

    // 如果当前没有播放，自动开始播放
    if (!this.playing) {
      this.#playNext()
    }
  }

  // 停止播放并清空队列
  stop() {
    this.#destroy()
    this.playing = false
    this.audioQueue = []
    this.stopped = true // 标记为已停止
    console.log('Playback stopped and audioQueue cleared.', this.audioQueue)
  }

  #destroy() {
    if (this.#innerAudioContext) {
      try {
        this.#innerAudioContext.pause()
        this.#innerAudioContext.destroy()
        this.#innerAudioContext = null
        console.log('Audio instance destroyed successfully.')
      } catch (e) {
        console.log('Audio instance destruction failed.')
      }
    }
  }

  /**
   * 恢复播放状态，允许添加音频并播放
   */
  start() {
    this.stopped = false
    console.log('Player restarted. You can now add audio to the audioQueue.')
  }

  /**
   * 设置 onPlay 事件回调
   * @param {Function} callback - 回调函数，参数为当前播放的音频路径
   */
  onPlay(callback) {
    this.onPlayCallback = callback
  }

  /**
   * 设置 onStop 事件回调
   * @param {Function} callback - 回调函数
   */
  onStop(callback) {
    this.onStopCallback = callback
  }
}

export default AudioPlayer
