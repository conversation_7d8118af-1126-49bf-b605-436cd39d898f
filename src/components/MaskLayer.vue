<template>
  <view :class="['mask-layer', { 'fade-out': !display }]" v-if="destoryOnHide"
    @animationend="handleAnimationEnd">
    <!-- image-wrap元素初始高度设置必须大于图片宽度缩放后的实际高度,否则图片高度计算错误会被压缩 -->
    <view class="image-wrap">
      <image class="w-full" src="@/static/media/maskLayer.png" mode="widthFix" />
    </view>
    <view class="load-animation">
      <LoadAnimation />
    </view>
  </view>
</template>

<script>
  import LoadAnimation from "@/components/LoadAnimation"

  // const props = defineProps({ show: Boolean, destoryOnHide: Boolean }) // ['show','destoryOnHide']

  export default {
    options: {
      virtualHost: true // 在vue3组合式api中该属性不知道怎么设置,没查到
    },
    components: { LoadAnimation },
    props: {
      show: {
        type: Boolean,
        default: true
      },
      // destoryOnHide: {
      //   type: Boolean,
      //   default: true
      // }
    },
    data() {
      return {
        // 开始动画
        display: this.show,
        // 动画结束后销毁元素
        destoryOnHide: true
      }
    },
    watch: {
      show(newValue) {
        if (newValue) {
          this.display = true
          this.destoryOnHide = true
        } else {
          this.display = false
        }
      }
    },
    methods: {
      handleAnimationEnd() {
        this.destoryOnHide = false
      }
    }
  }
</script>

<style lang="scss" scoped>
  .mask-layer {
    position: fixed;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    top: 0;
    left: 0;
    z-index: 99;
    background-color: #212135;

    .image-wrap {
      width: 100vw;
      min-height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    }

    .load-animation {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .fade-out {
    animation: fadeOut 1s forwards;
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }
</style>