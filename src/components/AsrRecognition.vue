<template>
  <!-- 状态显示 -->
  <view class="flex flex-col items-center mb-10">
    <!-- 连接中状态 -->
    <view v-show="!isAsrReady || !isRecordReady" class="flex flex-col items-center">
      <image src="../static/media/phoneMaskConnect.png" class="w-[40rpx] h-[40rpx] mb-10" />
      <view class="text-center  text-white text-[32rpx]  font-medium">连接中</view>
    </view>

    <!-- 等待中状态 -->
    <view v-show="isAsrReady && isRecordReady && !isRecognition && !isMuted && !isPlayRecord"
      class="flex flex-col items-center">
      <image src="../static/media/phonecallWait.png" class="mb-10 w-[96rpx] h-[24rpx]" />
      <view class="text-center text-white text-[32rpx]font-medium">您可以开始说话</view>
    </view>

    <!-- 聆听中状态 -->
    <view v-show="isAsrReady && isRecordReady && isRecognition && !isMuted && !isPlayRecord"
      class="flex flex-col items-center">
      <image src="../static/media/phoneMaskLoad.png" class="mb-10 w-[96rpx] h-[44rpx]" />
      <view class="text-center  text-white text-[32rpx] font-medium">正在聆听中...</view>
    </view>

    <!-- (isAsrReady && isRecordReady && !isRecognition && !isMuted && isPlayRecord) || (isPlayRecord && !isAsrReady) -->
    <!-- AI处理中显示打断状态 -->
    <view v-show="isPlayRecord  && !isMuted"
      class="flex flex-col items-center" @click="handleAudioClose">
      <view class="w-[36rpx] h-[36rpx] bg-[#fff] rounded-[8rpx] mb-10"></view>
      <!-- <view class="text-center  text-white text-[32rpx] font-medium">说话或点击打断</view> -->
      <view class="text-center  text-white text-[32rpx] font-medium">点击打断</view>
    </view>

    <!-- 已静音状态 -->
    <view v-show="isAsrReady && isRecordReady && isMuted" class="flex flex-col items-center">
      <view class="text-center  text-white text-[32rpx] font-medium">已静音</view>
    </view>
  </view>

</template>

<script setup>
import { onUnmounted, ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { isUndefined } from '@/utils'
import SpeechTranscription from "@/utils/st"
import { generateSilentPCM16 } from '@/utils/index';
let st = null; // 语音识别
const isRecordReady = ref(false) // 录音功能是否准备
const isAsrReady = ref(false) // 识别功能是否准备
const isRecognition = ref(false) // 是否识别语音中
const isMuted = ref(false) // 是否禁音中
const isPlayRecord = ref(false) // 人物是否说话中

const emit = defineEmits([
  'recognition-end',        // 识别结束
  'audio-close', // 关闭语音播报
  'changeRecognitionStatus', // 修改识别状态
]);

// 状态改变时修改外面的数据
watch(isAsrReady, () => {
  emit('changeRecognitionStatus', {
    isAsrReady: isAsrReady.value,
    isRecordReady: isRecordReady.value,
    isRecognition: isRecognition.value,
    isMuted: isMuted.value
  })
})
watch(isRecordReady, () => {
  emit('changeRecognitionStatus', {
    isAsrReady: isAsrReady.value,
    isRecordReady: isRecordReady.value,
    isRecognition: isRecognition.value,
    isMuted: isMuted.value
  })
})
watch(isRecognition, () => {
  emit('changeRecognitionStatus', {
    isAsrReady: isAsrReady.value,
    isRecordReady: isRecordReady.value,
    isRecognition: isRecognition.value,
    isMuted: isMuted.value
  })
})
watch(isMuted, () => {
  emit('changeRecognitionStatus', {
    isAsrReady: isAsrReady.value,
    isRecordReady: isRecordReady.value,
    isRecognition: isRecognition.value,
    isMuted: isMuted.value
  })
})

// 初始化录音/语音识别
onLoad(async () => {
  try {
    await privacyRecordAuth()
  } catch (error) {
    return
  }
  // 初始化录音
  initRecord()
})

// 初始化录音
let RecorderManager = uni.getRecorderManager();
const initRecord = () => {
  // 监听数据
  RecorderManager.onFrameRecorded(res => {
    // if (res.isLastFrame) {
    //   console.log("record done")
    // }
    // console.log('传递数据')
    if (!isMuted.value) {
      if (isPlayRecord.value) { // 在语音播报的时候需要传输静默数据,防止语音识别断开
        console.log('语音播报时传递数据')
        st.sendAudio(generateSilentPCM16(100))
        // st.sendAudio(res.frameBuffer)
      } else {
        if (isRecordReady.value && isAsrReady.value) {
          console.log('正常识别时传递数据')
          st.sendAudio(res.frameBuffer)
        }
      }
    } else { // 在禁音的时候需要传输静默数据,防止语音识别断开
      console.log('禁音时传递数据')
      st.sendAudio(generateSilentPCM16(100))
    }
    // if (isRecordReady.value && isAsrReady.value) { // 识别/录音准备完毕
    //   st.sendAudio(res.frameBuffer)
    // }
  })
  RecorderManager.onStart(() => {
    console.log("录音开始...")
    // 初始化语音识别
  })
  RecorderManager.onStop((res) => {
    console.log("录音结束...")
  })
  RecorderManager.onError((res) => {
    console.log("录音失败:" + res)
  })

  // 开始录音
  startRecord();
}

// 开始录音
const startRecord = () => {
  RecorderManager.start({
    duration: 600000,
    numberOfChannels: 1,
    sampleRate: 16000,
    format: "PCM",
    frameSize: 4
  })
  isRecordReady.value = true;
  initRecordRecognitionManager()
}

// 初始化语音识别  
const initRecordRecognitionManager = async () => {
  // getApp().initData = { // 初始数据
  //   voice: {
  //     code: "sicheng",
  //     expires: 1757680005,
  //     platform: "AliyunVoicePublic",
  //     projectKey: "cGWmNpUlN1f0oa5S",
  //     token: "882e92c118934d4cbeddd5eecd5fb058",
  //     url: "https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/tts"
  //   }
  // }
  const initData = getApp().initData;
  const { projectKey, token } = initData.voice
  const wssurl = 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1' // 阿里语音识别socket路径

  st = new SpeechTranscription({
    url: wssurl,
    appkey: projectKey,
    token
  })

  // 识别开始
  try {
    await st.start(st.defaultStartParams())
  } catch (e) {
    console.log("start failed:" + e)
    return
  }
  isAsrReady.value = true;

  st.on("started", (msg) => {
    console.log("识别已启动开始")
  })

  st.on("completed", (msg) => {
    console.log("识别完成:", msg)
  })

  st.on("begin", (msg) => { // 用到
    console.log("识别开始:", msg)
    isRecognition.value = true;
    // 识别开始后能断掉语音播报
    if (isPlayRecord.value) {
      isPlayRecord.value = false;
      emit('audio-close');
    }
  })

  st.on("changed", (msg) => { // 用到
    console.log("识别改变:", JSON.parse(msg).payload.result)
  })

  st.on("end", (msg) => { // 用到
    let result = JSON.parse(msg).payload.result;
    console.log("识别结束:", result)
    if (!isMuted.value && !isPlayRecord.value) {
      isRecognition.value = false;
      emit('recognition-end', result)
    }
  })

  st.on("closed", async () => {
    console.log("识别关闭")
    isAsrReady.value = false;
    try {
      await st.start(st.defaultStartParams())
      isAsrReady.value = true;
    } catch (e) {
      console.log("start failed:" + e)
      return
    }
    // uni.showToast({
    //   icon: 'none',
    //   title: '识别连接断开,重连中'
    // })
  })

  st.on("failed", (msg) => {
    console.log("识别失败:", msg)
  })
}

// 卸载
onUnmounted(() => {
  if (st) st.close();
  RecorderManager.stop();
  console.log('卸载')
})

// 修改禁音
const changeMuted = () => {
  isMuted.value = !isMuted.value
}

// 修改播报状态
const changePlay = (status) => {
  if (status) {
    isPlayRecord.value = status
  } else {
    setTimeout(() => {
      isPlayRecord.value = status
    }, 500);
  }
}

// 打断播报
const handleAudioClose = () => {
  isPlayRecord.value = false;
  emit('audio-close');
}

// 隐私/录音授权
const privacyRecordAuth = () => {
  return new Promise((resolve, reject) => {
    // 隐私授权
    wx.requirePrivacyAuthorize({
      success: (res) => {
        // console.log('用户同意了隐私协议 或 无需用户同意隐私协议')

        // 获取麦克风授权
        uni.getSetting({
          success: (res) => {
            // console.log(
            //   'getSetting:',
            //   res.authSetting,
            //   res.authSetting['scope.record']
            // )
            // 未授权过
            if (isUndefined(res.authSetting['scope.record'])) {
              uni.authorize({
                scope: 'scope.record',
                success: () => {
                  console.log('已授权使用麦克风')
                  // 同意授权
                  resolve()
                },
                fail: (error) => {
                  console.log('未授权隐私权限或拒绝使用麦克风', error)
                  // errno: 104, errMsg: "requirePrivacyAuthorize:fail privacy permission is not authorized"
                  refusalAuthHandle()
                  reject()
                },
              })
              // 授权被拒绝
            } else if (res.authSetting['scope.record'] === false) {
              uni.showModal({
                content: '需要使用您的麦克风，是否前往设置页面授权？',
                success: ({ confirm, cancel }) => {
                  // {errMsg: "showModal:ok", cancel: false, confirm: true, content: null}
                  if (confirm) {
                    uni.openSetting({
                      success: (res2) => {
                        if (res2.authSetting['scope.record']) {
                          console.log('再次提示后已授权使用麦克风')
                          resolve()
                        } else {
                          // 仍未操作授权-提示
                          refusalAuthHandle()
                          reject()
                        }
                      },
                    })
                  } /* else if (cancel) {// 点击取消} */ else {
                    // 不同意-提示
                    refusalAuthHandle()
                    reject()
                  }
                },
              })
            } else {
              console.log('已授权使用麦克风')
              resolve()
            }
          },
        })
      },
      fail: (res) => {
        console.log('用户拒绝了隐私协议')
        refusalAuthHandle('您拒绝了隐私授权, 无法通过语音输入')
        return
      },
    })
  })
}

// 没有权限提示框
const refusalAuthHandle = (message) => {
  uni.showToast({
    icon: 'none',
    title: message || '您拒绝了使用麦克风,无法通过语音输入',
  })
}

defineExpose({
  changeMuted,
  changePlay
});
</script>
