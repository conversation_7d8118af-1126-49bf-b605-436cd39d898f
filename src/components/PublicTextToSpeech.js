import { deepAssign, uid } from '../utils'
// 并发多线程
class PublicTextToSpeech {
  constructor(url, parameters) {
    this.url = url
    this.parameters = parameters
    this.speechSynthesisQueue = []
    this.terminated = false
    this.onSynthesisCompletedCallback = null
    this.tasksSet = new Set()
  }

  speechSynthesis(text) {
    if (this.terminated) {
      console.error(
        'Cannot add text. The synthesizer is terminated. Call `startSynthesis` to enable adding text synthesis.'
      )
      return
    }

    if (!text?.trim()) {
      console.warn('转换文本为空')
      return
    }
    // 回复消息未播放完毕再次输入新内容
    const id = '_' + uid() // id或者class不能以数字开头
    this.speechSynthesisQueue.push({
      id,
      filePath: '',
      text,
    })
    // console.log('speechSynthesisQueue', this.speechSynthesisQueue)
    const requestTask = uni.request({
      url: this.url, // 'https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts'
      headers: {
        // 'X-NLS-Token': token,
      },
      data: deepAssign(
        {
          // appkey,
          // token,
          text,
          // voice,
          format: 'wav',
          sample_rate: 16000,
          speech_rate: 100,
        },
        this.parameters,
        {
          text,
        }
      ),
      responseType: 'arraybuffer',
      success: (res) => {
        this.tasksSet.delete(requestTask)

        if (!this.terminated) {
          const filePath = `${wx.env.USER_DATA_PATH}/${id}.wav`
          uni.getFileSystemManager().writeFile({
            filePath,
            encoding: 'binary',
            data: res.data,
            success: () => {
              if (!this.terminated) {
                // console.log(this.speechSynthesisQueue)
                const currentItem = this.speechSynthesisQueue.find(
                  (item) => item.id === id
                )
                if (currentItem) {
                  currentItem.filePath = filePath
                }

                while (this.speechSynthesisQueue.length) {
                  const { filePath, text } = this.speechSynthesisQueue[0]
                  if (filePath) {
                    this.onSynthesisCompletedCallback &&
                      this.onSynthesisCompletedCallback(filePath, text)
                    this.speechSynthesisQueue.shift()
                  } else {
                    // 异步请求返回先后不确定, 请求并发, 播放必须顺序执行, 后面请求先返回也需等待前面请求
                    break
                  }
                }
              }
            },
            fail: (err) => {
              console.error('合成音频文件写入错误', err, text)
            },
          })
        } else {
          this.speechSynthesisQueue.shift()
        }
      },
      fail: (err) => {
        console.log('语音合成失败：', err, this.initData.voice)
        // uni.showToast({
        //   title: '语音合成失败',
        //   icon: 'error',
        // })
      },
    })

    this.tasksSet.add(requestTask)
  }

  startSynthesis() {
    this.terminated = false
    console.log(
      'Synthesizer restarted. You can now add text to the speechSynthesis.'
    )
  }

  stopSynthesis() {
    this.terminated = true
    this.speechSynthesisQueue = []
    this.tasksSet.forEach((task) => {
      task.abort()
    })
    this.tasksSet.clear()
  }

  onSynthesisCompleted(callback) {
    this.onSynthesisCompletedCallback = callback
  }
}

export default PublicTextToSpeech
