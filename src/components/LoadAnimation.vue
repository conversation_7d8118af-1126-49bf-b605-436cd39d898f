<template>
  <view class="loading_box">
    <view class="loading">
      <view class="bar"></view>
      <view class="bar"></view>
      <view class="bar"></view>
      <view class="bar"></view>
      <view class="bar"></view>
      <view class="bar"></view>
      <view class="bar"></view>
      <view class="bar"></view>
      <view class="bar"></view>
      <view class="bar"></view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .loading_box {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .loading {
    width: 240rpx;
    height: 160rpx;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .bar {
    --bg: #fff;
    --delay: 1s;
    background-color: var(--bg);
    width: 14rpx;
    height: 160rpx;
    border-radius: 12rpx;
    animation: loader 1.5s ease-in-out infinite;
    animation-delay: var(--delay);
  }

  @keyframes loader {
    0%,
    100% {
      height: 4rpx;
    }

    50% {
      height: 80rpx;
    }
  }

  .bar:nth-child(1),
  .bar:nth-child(10) {
    --bg: #5a6af6;
    --delay: 1s;
  }

  .bar:nth-child(2),
  .bar:nth-child(9) {
    --bg: #9474f2;
    --delay: 0.8s;
  }

  .bar:nth-child(3),
  .bar:nth-child(8) {
    --bg: #a1aaf9;
    --delay: 0.6s;
  }

  .bar:nth-child(4),
  .bar:nth-child(7) {
    --bg: #bdc4fc;
    --delay: 0.4s;
  }

  .bar:nth-child(5),
  .bar:nth-child(6) {
    --bg: #9474f2;
    --delay: 0.2s;
  }
</style>
