<template>
	<view class="px-6 py-5 text-gray-600 text-sm avatarNickName">
		<view class="mb-3">获取您的昵称、头像</view>
		<view class="flex items-center py-4">
			<label class="w-1/4 text-center">头像</label>
			<button class="px-0 w-10 h-10 ms-0 after:content-none"
				open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
				<image class="w-10 h-10"
					:src="avatarUrl || '../static/media/avatar.jpg'" mode="scaleToFill" />
			</button>
		</view>
		<view class="flex items-center py-4 mb-8">
			<label class="w-1/4 text-center">昵称</label>
			<view class="flex-1" @touchstart.stop="handleTouchInput">
				<input type="nickname" :value="nickName" :focus="focus"
					placeholder="点击输入框，输入昵称" :maxlength="20" @blur="handleGetNickname" />
			</view>
		</view>
		<view class="flex justify-evenly">
			<!-- <up-button class="!w-32" text="取消" @click="cancelHandle"></up-button> -->
			<up-button class="nickNameBtn" text="保存" @click="saveHandle"></up-button>
		</view>
	</view>
</template>
<script>
	export default {
		options: {
			styleIsolation: 'shared', // 解除样式隔离
		},
	};
</script>

<script setup>
	import { ref, watch } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'

	const props = defineProps(['data'])
	const emit = defineEmits(['cancel', 'save'])

	const avatarUrl = ref('')
	const nickName = ref('')
	const focus = ref(false)

	onLoad(() => {
		// const { avatarUrl, nickName } = this.loginInfo
		// this.avatarUrl = avatarUrl || ''
		// this.nickName = nickName || ''
	})

	watch(props.data, (newVal, oldVal) => {
		if (newVal) {
			avatarUrl.value = newVal.avatarUrl
			nickName.value = newVal.nickname
		}
	})

	// 获取头像
	const onChooseAvatar = (e) => {
		avatarUrl.value = e.detail.avatarUrl
	}
	// 获取昵称
	const handleGetNickname = (e) => {
		nickName.value = e.detail.value.trim()
	}

	// 昵称不会自动触发隐私授权
	const handleTouchInput = () => {
		focus.value = false // 必须否则首次输入后输入框无法再获得焦点
		if (uni.requirePrivacyAuthorize) {
			uni.requirePrivacyAuthorize({
				success: () => {
					console.log('用户同意了隐私协议 或 无需用户同意隐私协议')
					// 用户同意隐私协议后给昵称input聚焦
					focus.value = true
				},
				fail: (res) => {
					console.log('用户拒绝了隐私协议')
				},
			})
		} else {
			focus.value = true
		}
	}

	const cancelHandle = () => {
		emit('cancel')
	}

	const saveHandle = () => {
		if (!avatarUrl.value) {
			uni.showToast({
				title: '请选择头像',
				duration: 2000,
				icon: 'none',
			})
			return
		}
		if (!nickName.value) {
			uni.showToast({
				title: '请填写昵称',
				duration: 2000,
				icon: 'none',
			})
			return
		}
		emit('save', {
			avatarUrl: avatarUrl.value,
			nickName: nickName.value,
		})
	}
</script>

<style lang="scss" scoped>
	.avatarNickName {
		color: #080642;
		font-size: 16px;
	}

	.nickNameBtn {
		margin-top: 40px;
		background: linear-gradient(90deg, #82E9FA 0%, #4B71F2 100%) !important;
		border-radius: 25px !important;
		color: #fff;
	}
</style>
