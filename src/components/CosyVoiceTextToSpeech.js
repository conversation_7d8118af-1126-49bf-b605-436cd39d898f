class CosyVoiceTextToSpeech {
  #taskId
  #activePromise
  #ws
  #fs

  constructor(url, parameters) {
    this.url = url
    this.parameters = parameters
    this.speechSynthesisQueue = []
    this.terminated = false // 停止合成
    this.onSynthesisCompletedCallback = null
    this.unsynthesized = [] // 待合成文本
    this.synthesisQueue = [] // 每条合成语音二进制数据
    this.isSynthesisStarted = false // 开始一次合成
    this.isSendStopSynthesis = false // 是否发送停止合成指令
    this.fileWriting = false // arrayBuffer写入本地路径时, 是否正在写入
    // this.#taskId = '' // 会话ID

    this.#fs = wx.getFileSystemManager()
  }

  speechSynthesis(text) {
    if (this.terminated) {
      console.error(
        'Cannot add text. The synthesizer is terminated. Call `startSynthesis` to enable adding text synthesis.'
      )
      return
    }

    if (!text?.trim()) {
      console.warn('转换文本为空')
      return
    }

    // 可能存在语音合成未完成(超时未返回或异常造成stopSynthesis值未变更), 新消息已返回完成, 会造成该函数不会再触发; 理论上新消息onChunkReceived触发,上轮语音合成肯定已停止
    // if (this.terminated) {
    //   unsynthesizedTemp.push(plainMsg)
    //   return
    // }
    // while (unsynthesizedTemp.length) {
    //   this.#speechSynthesizer(unsynthesizedTemp.shift())
    // }

    this.unsynthesized.push(text.replace(/\s/g, ''))

    if (!this.isSynthesisStarted) {
      this.#speechSynthesizer(text)
    }
  }

  #speechSynthesizer(text) {
    if (this.terminated) {
      return
    }

    if (!this.isSynthesisStarted) {
      if (!this.#ws) {
        this.#activePromise = new Promise((resolve, reject) => {
          // 初始化 WebSocket 连接
          this.#ws = uni.connectSocket({
            url: `${this.url}?token=${this.parameters.token}`,
            // 'wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1?token=' + this.initData?.voice?.token,
            complete: () => {}, // uni.connectSocket 返回 socketTask 对象, 需要至少传入 success/fail/complete 参数中的一个
          })

          // 当 WebSocket 连接打开后发送 StartSynthesis 指令
          this.#ws.onOpen(() => {
            console.log(
              '%csynthesis-onOpen:',
              'color:#ff4d4f;',
              this.unsynthesized,
              { ...this.speechSynthesisQueue }
            )
            if (this.#ws.readyState === this.#ws.OPEN) {
              this.#taskId = this.#generateUUID()
              this.#sendStartSynthesis()
            }
          })
          // 当 WebSocket 连接发生错误时触发
          this.#ws.onError((err) => {
            reject(err)
            console.log('%csynthesis-onError:', 'color:#ff4d4f;', err)

            this.isSynthesisStarted = false
            // if (this.chatRecords.at(-1).complete) {
            //   this.terminated = false
            // }
            this.#ws = null // 重置 WebSocket 实例
          })
          // 当 WebSocket 连接关闭时触发
          this.#ws.onClose((err) => {
            resolve()
            console.log('%csynthesis-onClose:', 'color:#ff4d4f;', err)
            this.isSynthesisStarted = false
            this.isSendStopSynthesis = false
            this.#ws = null // 重置 WebSocket 实例

            if (this.terminated) {
              if (!this.fileWriting) {
                this.terminated = false
              }
              this.synthesisQueue = []
              this.unsynthesized = []
              this.speechSynthesisQueue = []
            }

            if (this.speechSynthesisQueue.length) {
              this.#arrayBufferToAudioUrl()
            }
          })
          // 当 WebSocket 收到消息时触发
          this.#ws.onMessage((event) => {
            const data = event.data
            // console...
            if (data instanceof ArrayBuffer) {
              console.log(
                '%csynthesis-onMessage:%s',
                'color:#597ef7;',
                data.byteLength
              )
            } else {
              const _body = JSON.parse(data)
              console.log(
                '%csynthesis-onMessage:',
                'color:#597ef7;',
                _body.header.name,
                _body.header.name === 'TaskFailed' ? _body.header : ''
              )
              // console.assert(_body.header.name !== 'TaskFailed', _body.header.status_message)
            }

            // 触发停止合成, 未发送停止指令
            if (this.terminated) {
              if (!this.isSendStopSynthesis) {
                console.log('%c触发停止合成', 'color:#85a5ff;')
                this.isSendStopSynthesis = true
                this.#sendStopSynthesis()
              }
              if (!(data instanceof ArrayBuffer)) {
                const body = JSON.parse(data) // 解析 JSON 数据
                if (
                  body.header.status === 20000000 &&
                  body.header.name === 'SynthesisCompleted'
                ) {
                  console.log('%c关闭语音合成ws连接', 'color:#85a5ff;')
                  this.isSynthesisStarted = false
                  this.isSendStopSynthesis = false
                  this.#ws.close()
                }
              }
              // synthesisQueue = []
              return
            }

            // 如果收到的是二进制数据
            if (data instanceof ArrayBuffer) {
              this.synthesisQueue.push(data)
            } else {
              // 如果收到的是文本消息
              const body = JSON.parse(data) // 解析 JSON 数据
              if (body.header.status === 20000000) {
                // 如果消息名称为 'SynthesisStarted' 指令 且状态为成功
                if (body.header.name === 'SynthesisStarted') {
                  // this.isSynthesisStarted = true // 更新合成状态为已经开始; 发送开始指令到触发开始合成事件间隔之间重复触发startSynthesis会造成错误TaskFailed:Gateway:TASK_STATE_ERROR:Got start directive while task is already started!,将改变状态放在发送同时

                  const textItem = this.unsynthesized.shift()
                  console.log(
                    '%csendRunSynthesis:%s',
                    'color:#85a5ff;',
                    textItem
                  )
                  this.#sendRunSynthesis(textItem) // 发送合成文本

                  setTimeout(() => {
                    // 需要等待sendRunSynthesis数据发送完毕, 否则可能会造成StopSynthesis 发送先于 RunSynthesis
                    this.isSendStopSynthesis = true
                    this.#sendStopSynthesis()
                  }, 40)
                }

                // 如果消息名称为 'SynthesisCompleted' 指令 且状态为成功
                if (body.header.name === 'SynthesisCompleted') {
                  const totalLength = this.synthesisQueue.reduce(
                    (acc, buffer) => acc + buffer.byteLength,
                    0
                  )

                  // 创建一个新的 ArrayBuffer 来存储所有数据
                  const mergedArray = new Uint8Array(totalLength)

                  // 当前写入位置的偏移量
                  let offset = 0

                  // 遍历所有的 ArrayBuffer
                  this.synthesisQueue.forEach((buffer) => {
                    // 将当前的 ArrayBuffer 写入到新的数组中
                    mergedArray.set(new Uint8Array(buffer), offset)
                    offset += buffer.byteLength // 更新偏移量
                  })

                  this.speechSynthesisQueue.push(mergedArray.buffer)
                  this.synthesisQueue = []

                  if (!this.writeFile) {
                    this.#arrayBufferToAudioUrl()
                  }

                  this.isSynthesisStarted = false // 更新合成状态为未开始
                  this.isSendStopSynthesis = false

                  if (this.unsynthesized.length) {
                    console.log(
                      '%csynthesisCompleted-startSynthesis',
                      'color:#9254de;'
                    )
                    this.#sendStartSynthesis()
                  } else {
                    // 未转换数组为空, 消息回复已完成, 可确定本次转换已完成, 关闭合成连接
                    if (this.#ws) {
                      console.log('%c关闭语音合成ws连接', 'color:#85a5ff;')
                      // 消息已返回完成
                      this.#ws.close()
                    }
                  }
                }

                // if(body.header.name === 'TaskFailed'){}
              }
            }
          })
        })
      } else {
        if (!this.isSynthesisStarted) {
          console.log('%celse-startSynthesis:', 'color:#9254de;')
          this.#sendStartSynthesis()
        }
      }
    }
  }

  // 只能包含小写字母和数字, 否则CosyVoice调用会报错Gateway:MESSAGE_INVALID:Invalid message id 'x...x'!
  #generateUUID() {
    let d = new Date().getTime()
    return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      let r = Math.random() * 16 // random number between 0 and 16
      r = (d + r) % 16 | 0
      d = Math.floor(d / 16)
      return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
    })
  }

  // 生成 WebSocket 消息的头部信息
  #getHeader() {
    return {
      message_id: this.#generateUUID(), // 当次消息请求ID, 随机生成32位唯一ID
      task_id: this.#taskId, // 整个实时语音合成的会话ID, 整个请求中需要保持一致, 32位唯一ID; 每次建立新连接时重新生成
      namespace: 'FlowingSpeechSynthesizer', // 访问的产品名称, 固定
      // name: '', // 指令名称, 包含StartSynthesis和StopSynthesis
      appkey: this.parameters.appkey, // 创建项目的Appkey
    }
  }

  #sendStartSynthesis() {
    const header = this.#getHeader() // 获取新的头部信息
    const params = {
      header: { ...header, name: 'StartSynthesis' }, // 更新消息头部信息中的名称为 'StartSynthesis'
      payload: {
        voice: this.parameters.voice, // 选择音色
        format: 'wav', // 文件格式 pcm
        sample_rate: 16000, // 采样率
        volume: 100, // 音量
        speech_rate: 0, // 语速
        pitch_rate: 0, // 音调
        enable_subtitle: true, // 是否启用字幕
        // platform: 'javascript'
      },
    }
    this.#ws.send({ data: JSON.stringify(params) }) // 发送开始合成消息
    this.isSynthesisStarted = true
  }

  // 发送要合成的文本
  #sendRunSynthesis(text) {
    if (this.#ws && this.isSynthesisStarted) {
      // 确保 WebSocket 连接已经建立并且合成已经开始
      const header = this.#getHeader() // 获取新的头部信息
      const params = {
        header: { ...header, name: 'RunSynthesis' }, // 更新消息头部信息中的名称为 'RunSynthesis'
        payload: {
          text, // 要合成的文本
        },
      }
      this.#ws.send({ data: JSON.stringify(params) }) // 发送消息
    } else {
      console.error('Cannot send RunSynthesis: Synthesis has not started')
    }
  }

  // 发送结束合成指令
  #sendStopSynthesis() {
    if (this.#ws && this.isSynthesisStarted) {
      // 确保 WebSocket 连接已经建立并且合成已经开始
      const header = this.#getHeader() // 获取新的头部信息
      const params = {
        header: { ...header, name: 'StopSynthesis' }, // 更新消息头部信息中的名称为 'StopSynthesis'
      }
      this.#ws.send({ data: JSON.stringify(params) }) // 发送消息
    } else {
      console.error('Cannot send StopSynthesis: Synthesis has not started')
    }
  }

  // 合成语音数据转换为本地文件
  #arrayBufferToAudioUrl() {
    if (
      !this.fileWriting &&
      !this.terminated &&
      this.speechSynthesisQueue.length
    ) {
      this.fileWriting = true
      const data = this.speechSynthesisQueue.shift()
      let filePath =
        wx.env.USER_DATA_PATH +
        '/' +
        this.#generateUUID().substring(16) +
        '.wav'
      console.log(`write file to ${filePath}`)
      // 文件写入本地路径需30/40ms左右
      this.#fs.writeFile({
        filePath,
        data,
        success: (res) => {
          this.fileWriting = false

          if (this.terminated) {
            if (!this.isSynthesisStarted) {
              this.terminated = false
            }
            return
          }

          // if (this.speechSynthesisQueue.length) {
          this.#arrayBufferToAudioUrl()
          // }

          this.onSynthesisCompletedCallback &&
            this.onSynthesisCompletedCallback(filePath)
        },
        fail: (res) => {
          console.log(`write file ${filePath} failed: ${res.errMsg}`)
        },
      })
    }
  }

  startSynthesis() {
    this.terminated = false
    console.log(
      'Synthesizer restarted. You can now add text to the speechSynthesis.'
    )
  }

  stopSynthesis() {
    this.terminated = true
    this.unsynthesized = []
    this.speechSynthesisQueue = []
    this.synthesisQueue = []
  }

  synthesizedPromise() {
    console.log('isAllSynthesized:', this.activePromises)
    return this.#activePromise
  }

  onSynthesisCompleted(callback) {
    this.onSynthesisCompletedCallback = callback
  }
}

export default CosyVoiceTextToSpeech
