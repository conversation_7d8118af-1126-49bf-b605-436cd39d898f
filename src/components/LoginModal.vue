<template>
  <up-modal class="login-modal !flex-none text-gray-800" :show="showLogin"
    :showConfirmButton="false" width="610rpx">
    <view
      class="text-sm w-full px-5 pt-20 pb-9 bg-gradient-to-b from-cyan-100 to-white relative rounded-2xl">
      <view
        class="absolute bg-white left-1/2 -translate-x-1/2 top-0 -translate-y-1/2 p-4 rounded-full shadow-xl shadow-cyan-600/30">
        <image class="w-16 h-16" src="@/static/media/logo-icon.png"
          mode="widthFix" />
      </view>
      <view class="text-center text-lg font-bold mb-2.5">
        授权须知
      </view>
      <view class="text-cente mb-6">
        为确保您能正常体验小程序功能，我们将获取您的手机号进行登录
      </view>
      <view>
        <up-button
          class="bg-gradient-to-r from-cyan-200 to-blue-500 before:content-none !h-9"
          open-type="getPhoneNumber" @getphonenumber="handleLogin"
          @touchstart="phoneNumberClick" shape="circle" text="同意"></up-button>
      </view>
    </view>
  </up-modal>
</template>
<script>
  import { registerApi } from '@/api'
  import { useStore } from '@/stores/mainStore'

  const app = getApp()
  const store = useStore()

  export default {
    options: {
      styleIsolation: 'shared' // 解除样式隔离,否则u-modal穿透样式不生效
      // virtualHost: true // 允许使用虚拟host;启用后可以通过 mergeVirtualHostAttributes合并合并组件虚拟节点外层属性
    },
    data() {
      return {
        showLogin: false
      }
    },
    beforeCreate() {
      app.globalData.loginPromise.finally(() => {
        console.log('loginModal:', app.globalData, { ...store.userinfo })
        const { token, expires } = store.userinfo
        if (!token || (token && +new Date() > expires)) {
          this.showLogin = true
        }
      })
    },
    methods: {
      // 用户可能拒绝官方隐私授权弹窗，为了避免过度弹窗打扰用户，开发者再次调用隐私相关接口时，若距上次用户拒绝不足10秒，将不再触发弹窗，直接给到开发者用户拒绝隐私授权弹窗的报错 https://developers.weixin.qq.com/miniprogram/dev/framework/user-privacy/PrivacyAuthorize.html#%E5%85%AD%E3%80%81%E5%AE%98%E6%96%B9%E9%9A%90%E7%A7%81%E5%BC%B9%E7%AA%97%E5%8A%9F%E8%83%BD%E8%AF%B4%E6%98%8E
      phoneNumberClick() {
        // console.log('phoneNumberTouchstart', e)
        uni.getPrivacySetting({
          success: (res) => {
            console.log('查询隐私授权:', res)
            if (res.needAuthorization) {
              uni.requirePrivacyAuthorize({
                success: () => {
                  console.log('用户同意了隐私协议 或 无需用户同意隐私协议')
                  // 用户同意隐私协议后给昵称input聚焦
                },
                fail: (res) => {
                  console.log('用户拒绝了隐私协议')
                },
              })
              e.stopPropagation()
            }
          },
        })
      },
      handleLogin(e) {
        if (e.detail.code) {
          uni.login({
            success: (res) => {
              if (res.code) {
                registerApi({
                  data: {
                    // avatar: '',
                    nickname: '微信用户',
                    mobileCode: e.detail.code,
                    jsCode: res.code,
                  },
                  loading: true,
                })
                  .then((res) => {
                    // app.globalData.logged = true
                    app.globalData.userinfo = res.data
                    store.updateUserinfo(res.data)
                    this.showLogin = false

                    const currentPage = getCurrentPages().at(-1)
                    // 重新执行onLoad
                    currentPage.onLoad(currentPage.options);
                    // uni.redirectTo({
                    //   url: '/' + getCurrentPages().at(-1)?.route, // currentPage.options需序列化才能拼接?a=1&b=2
                    // })
                  })
                  .catch((err) => {
                    console.log('用户授权注册错误:', err)
                    uni.showToast({
                      icon: 'error',
                      title: '注册失败',
                    })
                  })
              } else {
                console.error('wx.login获取登录凭证(code)失败:', res)
                uni.showToast({
                  icon: 'error',
                  title: '注册失败',
                })
              }
            },
            fail: (err) => {
              console.error('wx.login获取登录凭证(code)失败:', err)
              uni.showToast({
                title: `${err.errno}:${err.errMsg}`,
                icon: 'none',
              })
            },
          })
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  // 需启用mergeVirtualHostAttributes,否则无法穿透
  .login-modal {
    :deep(.u-popup__content) {
      border-radius: 32rpx !important;

      &,
      .u-modal {
        overflow: visible !important;
      }

      .u-modal__content {
        padding: 0 !important;

        &+.u-line {
          display: none;
        }
      }
    }
  }
</style>