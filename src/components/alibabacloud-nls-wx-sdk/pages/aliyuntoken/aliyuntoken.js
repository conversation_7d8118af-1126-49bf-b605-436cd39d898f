// pages/aliyuntoken/aliyuntoken.js

const app = getApp()
const getToken = require("../../utils/token").getToken

Page({
    data: {
        token: ""
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        if (!this.data.token) {
            this.setData({
                token : "未获取token"
            })
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },

    onTokenGet : async function() {
        try {
            let token = await getToken(app.globalData.AKID,
                 app.globalData.AKKEY)
            this.setData({
                token: token
            })
        } catch (e) {
            console.log("error on get token:", JSON.stringify(e))
        }
    }
})