import { deepAssign, uid } from '../utils'
import { TextEncoder } from '@kayahr/text-encoding'

// 并发,多线程,有最大连接数限制
class XFTextToSpeech {
  #synthesisTasks
  constructor(url, parameters, maxConcurrency = 2) {
    this.url = url
    this.parameters = parameters
    this.#synthesisTasks = Array.from({ length: maxConcurrency }, () => null)
    this.terminated = false
    this.unsynthesizedText = []
    this.speechSynthesisQueue = []
    this.onSynthesisCompletedCallback = null
    this.activePromises = []
  }

  // sentence Synthesis
  speechSynthesis(text) {
    if (this.terminated) {
      console.error(
        'Cannot add text. The synthesizer is terminated. Call `startSynthesis` to enable adding text synthesis.'
      )
      return
    }

    if (!text?.trim()) {
      console.warn('转换文本为空')
      return
    }

    this.unsynthesizedText.push(text)

    // 如果判断是否有线程空出, 调用合成, 会触发并发超限(不在合成中启用合成)
    if (this.#synthesisTasks.every((item) => !item || item?.readyState === 3)) {
      this.#speechSynthesizer(this.unsynthesizedText.shift())
    }
  }

  #speechSynthesizer(text) {
    const socketPromise = new Promise((resolve, reject) => {
      const id = '_' + uid()
      this.speechSynthesisQueue.push({
        id,
        filePath: '',
        text,
      })
      const synthesizedQueue = [] // 存储已合成base64语音

      // 不能直接查找任务, 需重新建立连接并赋值给原始对象, 小程序WebSocket并发限制,同时最多发起5个(微信SockTask请求关闭后或赋值为null该并发数量并不会被释放,需要将新连接任务赋值给已关闭SockTask)
      const completeIndex = this.#synthesisTasks.findIndex(
        (item) => !item || item?.readyState === 3
      )

      this.#synthesisTasks[completeIndex] = uni.connectSocket({
        url: this.url, // ws(s)://cbm01.cn-huabei-1.xf-yun.com/v1/private/s06a6b848,
        success: (res) => {
          // console.log('websocket success', res)
        },
        complete: (res) => {
          // console.log('websocket complete', res)
        }, // uni.connectSocket 返回 socketTask 对象, 需要至少传入 success/fail/complete 参数中的一个
      })

      const socketTask = this.#synthesisTasks[completeIndex]

      // const socketTask = uni.connectSocket({
      //   url: this.url,
      //   complete: (res) => {},
      // })

      // this.#synthesisTasks[
      //   this.#synthesisTasks.findIndex((item) => !item || item?.readyState === 3)
      // ] = socketTask

      // 当 WebSocket 连接打开后发送 StartSynthesis 指令
      socketTask.onOpen(() => {
        console.log(
          '%csynthesis-onOpen:',
          'color:#f97316;',
          this.unsynthesizedText,
          // JSON.stringify(this.speechSynthesisQueue),
          socketTask.OPEN,
          socketTask.readyState
        )

        // 有终止合成操作, 这里直接结束, 暂不考虑复用当前socket发送新文本合成
        if (this.terminated) {
          socketTask.close()
          return
        }

        // readyState:1
        if (socketTask.readyState === socketTask.OPEN) {
          const params = deepAssign(
            {
              header: {
                // app_id: projectKey, // 必传
                status: 2, // 0开始 1中间 2结束
                // res_id: code, // 必传
              },
              parameter: {
                tts: {
                  vcn: 'x5_clone',
                  // volume: 50, // 音量
                  // rhy: 0,
                  // pybuffer: 1,
                  // speed: 50, // 语速
                  // pitch: 50, // 语调
                  // bgs: 1,
                  // reg: 0,
                  // rdn: 0,
                  audio: {
                    encoding: 'lame', // speex-wb // 音频编码	lame, speex, opus, opus-wb, speex-wb
                    sample_rate: 24000, // 采样率 // 必传
                    // channels: 1,
                    // bit_depth: 16, // 位深
                    // frame_size: 0,
                  },
                },
              },
              payload: {
                text: {
                  encoding: 'utf8', // 文本编码 utf8, gb2312, gbk // 必传
                  // compress: 'raw', // 文本压缩格式	raw, gzip
                  // format: 'plain', // 文本格式	plain, json, xml
                  status: 2,
                  seq: 0,
                },
              },
            },
            this.parameters,
            {
              payload: {
                text: {
                  text: wx.arrayBufferToBase64(new TextEncoder().encode(text)), // 文本数据 最小尺寸:1B, 最大尺寸:1200B	需base64编码，文本大小：300字
                },
              },
            }
          )
          socketTask.send({ data: JSON.stringify(params) })
        }
      })
      // 当 WebSocket 连接发生错误时触发
      socketTask.onError((err) => {
        console.log('%csynthesis-onError:', 'color:orange;', err)

        // 移除已完成请求
        this.activePromises = this.activePromises.filter(
          (item) => item !== socketPromise
        )
        this.abort()

        reject(err)

        uni.showToast({
          title: '语音合成失败',
          icon: 'error',
        })
      })
      // 当 WebSocket 收到消息时触发
      socketTask.onMessage((event) => {
        if (this.terminated) {
          socketTask.close()
          return
        }

        const data = JSON.parse(event.data)

        if (data.header.code !== 0) {
          console.error('synthesis-onMessage-error:', data, this.terminated)
          // 并发超限 header: {code: 11203, message: "licc failed", sid: "ase000ec3a2@dx193a07b1fa7b8e0882", status: 0}
          // uni.showToast({
          //   title: '语音合成失败',
          //   icon: 'error',
          // })
          return
        } else {
          // console.log(
          //   '%csynthesis-onMessage:%s',
          //   'color:#597ef7;',
          //   JSON.stringify(data.header),
          //   data.payload?.audio?.audio.length
          // )
        }

        if (data.header.status !== 0 && !this.soundOff) {
          // writeFile直接写入base64没有成功
          // synthesizedQueue.push(data.payload?.audio?.audio)
          synthesizedQueue.push(
            wx.base64ToArrayBuffer(data.payload?.audio?.audio)
          )
        }

        // 结束
        if (data.header.status === 2) {
          socketTask.close()

          console.log('synthesizedQueue:', synthesizedQueue)

          const totalLength = synthesizedQueue.reduce(
            (acc, buffer) => acc + buffer.byteLength,
            0
          )
          // 创建一个新的 ArrayBuffer 来存储所有数据
          const mergedArray = new Uint8Array(totalLength)
          // 当前写入位置的偏移量
          let offset = 0
          // 遍历所有的 ArrayBuffer
          synthesizedQueue.forEach((buffer) => {
            // 将当前的 ArrayBuffer 写入到新的数组中
            mergedArray.set(new Uint8Array(buffer), offset)
            offset += buffer.byteLength // 更新偏移量
          })

          const filePath = `${wx.env.USER_DATA_PATH}/${id}.mp3`
          uni.getFileSystemManager().writeFile({
            filePath,
            // encoding: 'base64',
            // data: synthesizedQueue.join(''),
            encoding: 'binary',
            data: mergedArray.buffer,
            success: () => {
              if (!this.terminated) {
                const currentItem = this.speechSynthesisQueue.find(
                  (item) => item.id === id
                )
                if (currentItem) {
                  currentItem.filePath = filePath
                }

                console.log(
                  'speechSynthesisQueue:',
                  JSON.stringify(this.speechSynthesisQueue)
                )

                while (this.speechSynthesisQueue.length) {
                  const { filePath, text } = this.speechSynthesisQueue[0]

                  if (filePath) {
                    this.onSynthesisCompletedCallback &&
                      this.onSynthesisCompletedCallback(filePath, text)
                    this.speechSynthesisQueue.shift()
                  } else {
                    // 异步请求返回先后不确定, 请求并发, 播放必须顺序执行, 后面请求先返回也需等待前面请求
                    break
                  }
                }
              }
            },
            fail: (err) => {
              console.error('本地文件读取错误', err)
            },
          })
        }
      })
      // 当 WebSocket 连接关闭时触发
      socketTask.onClose((res) => {
        console.log('%csynthesis-onClose:', 'color:#f97316;', res)
        resolve()

        // 移除已完成请求
        this.activePromises = this.activePromises.filter(
          (item) => item !== socketPromise
        )

        if (this.unsynthesizedText.length) {
          if (
            this.#synthesisTasks.some(
              (item) => !item || item?.readyState === 3
            ) &&
            !this.terminated
          ) {
            this.#speechSynthesizer(this.unsynthesizedText.shift())
          }
        }
      })
    })

    this.activePromises.push(socketPromise)
    return socketPromise
  }

  // updateURL(url) {
  //   this.url = url
  // }

  startSynthesis() {
    this.terminated = false
    console.log(
      'Synthesizer restarted. You can now add text to the speechSynthesis.'
    )
  }

  stopSynthesis() {
    this.terminated = true
    this.unsynthesizedText = []
    this.speechSynthesisQueue = []
  }

  synthesizedPromise() {
    console.log('isAllSynthesized:', this.activePromises)
    return Promise.allSettled(this.activePromises)
  }

  onSynthesisCompleted(callback) {
    this.onSynthesisCompletedCallback = callback
  }
}

export default XFTextToSpeech
