<template>
  <!-- <view class="relative h-8 z-[10076]"> --><!-- 提升层级高于popup -->
  <view class="text-center w-full h-8 leading-8 mx-12 text-white" @touchstart="touchStartHandle"
    @touchmove="touchMoveHandle" @touchend="touchEndHandle" @touchcancel="onTouchcancel">{{ inputText }}</view>
  <!-- </view> -->
  <up-popup ref="popupRef" :customStyle="{ marginBottom: '186rpx' }" bgColor="transparent"
    :show="touchstart || recognizing" mode="bottom" overlayOpacity="transparent">
    <view class="up-popup-ref text-white"
    style="font-weight: 100;"
      :class="['flex flex-col pb-[24rpx] pt-[24rpx] items-center  mr-[24rpx] ml-[24rpx] justify-center text-sm min-h-14 rounded-[20rpx]', isTouchInPopup ? 'bg-[#333]' : 'bg-[#000]']">
      <up-loading-icon :show="!recording" mode="circle" size="28" color="#7E899B"></up-loading-icon>
      <view v-show="recording" class="mt-2 text-[32rpx]">正在录音...</view>
      <template>
        <image class="w-[376rpx] h-[84rpx] mb-2" src="@/static/media/recording.png" mode="widthFix">
        </image>
        <view class="text-[24rpx]">上滑取消发送</view>
      </template>
    </view>
  </up-popup>
  <!-- <up-toast ref="toastRef"></up-toast> -->
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { isUndefined } from '@/utils'
const instance = getCurrentInstance()
const isInPopup = ref(false)  
const plugin = requirePlugin("WechatSI")
const recordReco = plugin.getRecordRecognitionManager()
const touchstart = ref(false) // 是否被按下
const recording = ref(true) // 是否正在录音
const recognizing = ref() // 是否正在识别
// const toastRef = ref(null) // Toast提示
const recordStart = ref(false) // 调用开始录音(调用start到onStart会间隔一段时间)
let inputText = ref('按住说话')
const emits = defineEmits(['touchStart', 'touchEnd', 'onStart', 'onStop', 'cancelSend','moveIn'])
let isCancel = false
const popupRef = ref(null)
const istouchMoveHandle = ref(false)
// 防抖工具
let touchMoveTimer = null // 将定时器提取到外部，方便清除
function debounce(fn, delay = 50) {
  return function (...args) {
    if (touchMoveTimer) clearTimeout(touchMoveTimer)
    touchMoveTimer = setTimeout(() => {
      if(istouchMoveHandle.value){
        fn.apply(this, args)
      }
    }, delay)
  }
}

// 清除touchMove定时器的函数
const clearTouchMoveTimer = () => {
  if (touchMoveTimer) {
    clearTimeout(touchMoveTimer)
    touchMoveTimer = null
  }
}
const isTouchInPopup = ref(false)
// 手指移动时检测是否在 up-popup 区域
const _touchMoveHandle = (e) => {

  if (e.touches && e.touches.length) {
    const moveX = e.touches[0].clientX
    const moveY = e.touches[0].clientY
    
    if (!instance || !instance.proxy) return


    uni.createSelectorQuery().in(instance.proxy).select('.up-popup-ref').boundingClientRect(rect => {

      if (rect) {
        const inPopup =
          moveX >= rect.left &&
          moveX <= rect.right &&
          moveY >= rect.top &&
          moveY <= rect.bottom
        if (inPopup && !isTouchInPopup.value) {
          console.log('进入')
          isInPopup.value = true
          inputText.value = '语音'
          let bool = false
          emits('moveIn',[bool,bool])
        } else if (!inPopup && isTouchInPopup.value) {
          console.log('离开')
          isInPopup.value = false
          inputText.value = '松开发送'
          let bool = true
          emits('moveIn',[bool,bool])
        }
        isTouchInPopup.value = inPopup
      }
    }).exec()
  }
}
const touchMoveHandle = debounce(_touchMoveHandle, 10)

onLoad(async (options) => {
  recordReco.onStart = res => {
    console.log('开始录音识别:', res)
    recording.value = true
    recognizing.value = true
    emits('onStart')
  }

  recordReco.onRecognize = res => {
    console.log('有识别结果返回:', res)
  }

  recordReco.onStop = res => {
    console.log('录音识别结束:', res)
    console.log("record file path", res.tempFilePath)
    console.log("result", res.result)
     recognizing.value = false
  if (isCancel) {
    isCancel = false
    return
  }
  emits('onStop', res)
  }

  recordReco.onError = res => {
    console.log('录音识别错误:', res)
    recognizing.value = false
    let title = ''
    switch (res.retcode) {
      case -30003:
        title = '讲话时间太短'
        break
      case -30004:
        title = '未识别到语音'
        break
      default:
        break
    }
    if (title) {
      // uni.showToast({
      //   title,
      //   icon: 'none',
      //   // mask: true, // 遮挡按钮会导致长按触摸事件中断,不触发touchend/touchcancel
      // })
      // toastRef.value.show({
      //   message: title,
      //   duration: 1200
      // })
    }
  }
})

// 说话
const touchStartHandle = (e) => {
  inputText.value = '松开发送'
  istouchMoveHandle.value = true
  emits('touchStart', async (resolve) => {
    if (resolve) {
      touchstart.value = true

      try {
        await privacyRecordAuth()
      } catch (error) {
        return
      }

      if (touchstart.value) {
        recordReco.start({ duration: 30000, lang: "zh_CN" })
        recordStart.value = true
      }
    }
  })
}

const touchEndHandle = (e) => {

  console.log('touchEndHandle');

  // 立即清除touchMove定时器，防止异常触发
  clearTouchMoveTimer()
  istouchMoveHandle.value = false
  let bool = true
  touchstart.value = false
  if (e && e.changedTouches && e.changedTouches.length && instance && instance.proxy) {
    const endX = e.changedTouches[0].clientX
    const endY = e.changedTouches[0].clientY
    uni.createSelectorQuery().in(instance.proxy).select('.up-popup-ref').boundingClientRect(rect => {
      if (rect) {
        const inPopup =
          endX >= rect.left &&
          endX <= rect.right &&
          endY >= rect.top &&
          endY <= rect.bottom
        if (inPopup) {
          // 在 up-popup 区域，终止录音且不发送消息
          isCancel = true
          if (recordStart.value) {
            recordStart.value = false
            recording.value = false
            recordReco.stop()
          }
          // emits('cancelSend')
          isTouchInPopup.value = false
          inputText.value = '按住说话'
          emits('moveIn',[bool,!bool])
          return
        }
      }
      // 离开区域，保持原逻辑
      emits('touchEnd')
      if (recordStart.value) {
        recordStart.value = false
        recording.value = false
        recordReco.stop()
      }
      inputText.value = '按住说话'
      emits('moveIn',[bool,!bool])

      isTouchInPopup.value = false
    }).exec()
  } else {
    // fallback: 没有触摸点或 instance，保持原逻辑
    emits('touchEnd')
    if (recordStart.value) {
      recordStart.value = false
      recording.value = false
      recordReco.stop()
    }
        inputText.value = '按住说话'
      emits('moveIn',[bool,!bool])

    isTouchInPopup.value = false
  }
}


// 初次授权弹窗会造成触摸事件中断,不触发touchend结束录音
const onTouchcancel = () => {
  // 清除touchMove定时器，防止异常触发
  clearTouchMoveTimer()
  istouchMoveHandle.value = false
  touchstart.value = false
}

// 隐私/录音授权
const privacyRecordAuth = () => {
  return new Promise((resolve, reject) => {
    // 隐私授权
    wx.requirePrivacyAuthorize({
      success: (res) => {
        // console.log('用户同意了隐私协议 或 无需用户同意隐私协议')

        // 获取麦克风授权
        uni.getSetting({
          success: (res) => {
            // console.log(
            //   'getSetting:',
            //   res.authSetting,
            //   res.authSetting['scope.record']
            // )
            // 未授权过
            if (isUndefined(res.authSetting['scope.record'])) {
              uni.authorize({
                scope: 'scope.record',
                success: () => {
                  console.log('已授权使用麦克风')
                  // 同意授权
                  resolve()
                },
                fail: (error) => {
                  console.log('未授权隐私权限或拒绝使用麦克风', error)
                  // errno: 104, errMsg: "requirePrivacyAuthorize:fail privacy permission is not authorized"
                  refusalAuthHandle()
                  reject()
                },
              })
              // 授权被拒绝
            } else if (res.authSetting['scope.record'] === false) {
              uni.showModal({
                content: '需要使用您的麦克风，是否前往设置页面授权？',
                success: ({ confirm, cancel }) => {
                  // {errMsg: "showModal:ok", cancel: false, confirm: true, content: null}
                  if (confirm) {
                    uni.openSetting({
                      success: (res2) => {
                        if (res2.authSetting['scope.record']) {
                          console.log('再次提示后已授权使用麦克风')
                          resolve()
                        } else {
                          // 仍未操作授权-提示
                          refusalAuthHandle()
                          reject()
                        }
                      },
                    })
                  } /* else if (cancel) {// 点击取消} */ else {
                    // 不同意-提示
                    refusalAuthHandle()
                    reject()
                  }
                },
              })
            } else {
              console.log('已授权使用麦克风')
              resolve()
            }
          },
        })
      },
      fail: (res) => {
        console.log('用户拒绝了隐私协议')
        refusalAuthHandle('您拒绝了隐私授权, 无法通过语音输入')
        return
      },
    })
  })
}

const refusalAuthHandle = (message) => {
  uni.showToast({
    icon: 'none',
    title: message || '您拒绝了使用麦克风,无法通过语音输入',
  })
}
</script>
