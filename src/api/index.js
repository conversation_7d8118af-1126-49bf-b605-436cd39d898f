import { primaryRequest, testRequest } from '../utils/request'

import { baseURL } from '@/assets/config'

// 通过凭证换取用户登录状态信息
export const wechatAuth = (config) =>
  primaryRequest.post('/xuc/sso/app/wechat/user/auth', config) // /xuc/sso/customer/wechat/auth

// 注册
export const registerApi = (config) =>
  primaryRequest.post('/xuc/sso/app/wechat/register', config) // + xuc

// 获取聊天初始数据
export const getChatInit = (config) =>
  primaryRequest.get('/ichat/chat/init', config)

// 获取详细信息
export const getSimbotInfo = (config) =>
  primaryRequest.get('/ichat/chat/simbot/detail', config)

// 获取详细信息
export const getAliyunConfig = (config) =>
  testRequest.get('/app/app/voice/token', config)

// 常见问题
// export const commonQuestion = (config) =>
//   primaryRequest.get('/ichat/chat/plugins/question', config)

// 聊天历史记录
export const chatMessageHistory = (config) =>
  primaryRequest.post('/ichat/chat/message/history', config)

// 聊天
export const aiChat = (config) =>
  primaryRequest.post('/ichat/chat/send/chunked', config)

export const aiChatURL = baseURL + '/chat/send/chunked' // ichat

// 猜你想问
export const askingChunkedURL = baseURL + '/chat/plugins/asking/list' // ichat

// 分享统计
export const sharesCount = (config) =>
  primaryRequest.post('/customer/user/share', config) // /tinyapp

// 访问量统计
export const sessionView = (config) =>
  primaryRequest.post('/customer/user/view', config)

export const getUserInfo = (config) =>
  primaryRequest.get('/customer/user/getUserInfo', config)

// 获取联系人
export const getContacts = (config) =>
  primaryRequest.post('/customer/foot/footprint', config)

export const saveFeedbacks = (config) =>
  primaryRequest.post('/customer/feedback/submit', config)

// 语音合成
// export const speechSynthesizer = (config) =>
//   primaryRequest.get('https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/tts', config)

// 获取客服号码
export const getCustomer = (config) =>
  primaryRequest.get('/customer/feedback/getFeedbackPhone', config)

// 修改昵称
export const profileEdit = (config) =>
  primaryRequest.post('/xuc/app/user/nickname/edit', config)

// 大模型初始化
export const initPerson = (config) =>
  primaryRequest.get('/ichat/chat/init/person', config)

// 获取分享二维码
export const getShareQrcode = (config) =>
  primaryRequest.get('/xuc/app/user/wechat/qtcode', config)
