<script>
  import { wechatAuth } from '@/api'
  import { useStore } from '@/stores/mainStore'
  import { urlSearchParamsUtil } from '@/utils'

  export default {
    globalData: {
      // baseURL: ''
    },
    onLaunch(option) {
      console.log('App onLaunch', option)

      this.globalData.menuButtonClientRect = uni.getMenuButtonBoundingClientRect()

      this.globalData.store = useStore()


      this.globalData.loginPromise = this.login()
      // 错误时必须清空登录信息
      this.globalData.loginPromise.catch(() => {
        console.log('App login error')
        this.globalData.store.updateUserinfo({ expires: 0, lifetime: 0, token: '' })
      })

      const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate(function (res) {
        // 请求完新版本信息的回调
        console.log('获取新版本信息回调:', res)
      })
      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: function (res) {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate()
            }
          },
        })
      })
      updateManager.onUpdateFailed(function () {
        console.log('新版本下载失败')
        // 新的版本下载失败
        // wx.showModal({
        //   title: '更新提示',
        //   content: '新版本下载失败',
        //   showCancel: false
        // })
      })
    },
    onShow: function (option) {
      console.log('App onShow:', option)
      this.globalData.apiCategory = option.apiCategory
      this.globalData.scene = option.scene
      // 半屏小程序打开或分享进入
      const extraData = option?.referrerInfo?.extraData || option?.query
      if (extraData) {
        // option.scene 为小程序码场景值, option.query.scene 为小程序码参数
        if (extraData.scene) {
          const paramsUtil = urlSearchParamsUtil(decodeURIComponent(extraData.scene))
          Object.assign(extraData, paramsUtil('getAllAsObject'))
        }
        this.globalData.extraData = extraData
      }
      // 是否应保持option原始结构??
    },
    onHide: function () {
      // console.log('App onHide')
    },
    methods: {
      login: function () {
        return new Promise((resolve, reject) => {
          uni.login({
            success: (res) => {
              if (res.code) {
                wechatAuth({
                  data: {
                    jsCode: res.code,
                  },
                })
                  .then((res2) => {
                    console.log('%cauthInfo:%o', 'color:#52c41a;', res2)

                    // this.globalData.logged = true
                    this.globalData.userinfo = res2.data

                    this.globalData.store.updateUserinfo(res2.data)

                    resolve(res2)
                  })
                  .catch((err) => {
                    console.log(
                      '%c获取用户登录信息失败:',
                      'color:#f5222d;',
                      err
                    )
                    // if (err.errno === '4001') {
                    //   uni.redirectTo({
                    //     url: ''
                    //   })
                    // }

                    reject(err)
                  })
              } else {
                console.error(
                  '%cwx.login获取登录凭证(code)失败:',
                  'color:#f5222d;',
                  res
                )
                reject(res)
              }
            },
            fail: (err) => {
              console.error(
                '%cwx.login获取登录凭证(code)失败fail:',
                'color:#f5222d;',
                err
              )
              // uni.showToast({
              //   title: `${err.errno}:${err.errMsg}`,
              //   icon: 'none',
              // })
              reject(err)
            },
          })
        })
      },
    },
  }
</script>

<style lang="scss">
  // 公共css
  @import 'uview-plus/index.scss';

  // @import './assets/tachyons-custom.css';

  @import './assets/tailwind.css';

  @import './static/iconfont/iconfont.css';

  page {
    min-height: 100vh;
    // font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    // background: linear-gradient(to top, #FCFEFF, #C3E4FF 72%, #E3FFFA 100%);
    background-color: #24282F;
  }

  // .bg-page {
  //   background: linear-gradient(#FCFEFF, #C3E4FF 72%, #E3FFFA 100%);
  // }

  // 图标动画(让任何图标旋转,注意元素不能为display:inline)
  .icon-spin {
    animation: icon-spin 2s linear 0s infinite;
  }

  .icon-spin-reverse {
    --icon-animation-direction: reverse;
  }

  @keyframes icon-spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
</style>
