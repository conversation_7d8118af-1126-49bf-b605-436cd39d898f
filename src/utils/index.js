//数据类型判断
export const getClass = (object) => {
  return Object.prototype.toString.call(object).match(/^\[object\s(.*)\]$/)[1]
}

export const isUndefined = (value) => {
  return getClass(value) === 'Undefined'
}

export const isDate = (value) => {
  return getClass(value) === 'Date'
}

export const isString = (value) => {
  return getClass(value) === 'String'
}

export const isBoolean = (value) => {
  return getClass(value) === 'Boolean'
}

export const isNumeric = (value) => {
  const realStringObj = value && value.toString()
  return (
    !Array.isArray(value) && realStringObj - parseFloat(realStringObj) + 1 >= 0
  )
}

// JS对象深合并
export function deepMerge(...objects) {
  // if (typeof objects[0] !== 'object' || objects[0] === null || typeof objects[1] !== 'object' || objects[1] === null) return objects[0]
  const result = {}
  for (const obj of objects) {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (Array.isArray(obj[key])) {
          result[key] = [...obj[key]]
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          // 如果值是对象，则递归合并
          result[key] = deepMerge(result[key] || {}, obj[key])
        } else {
          // 如果值不是对象，直接赋值
          result[key] = obj[key]
        }
      }
    }
  }
  return result
}

// 判断是否时绝对路径
export function isAbsoluteURL(url) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url)
}

// URL解析
export function parseURL(url) {
  const regex =
    /^(?:([A-Za-z]+:))?(?:\/\/)?(([^:\/?#]+)(?::(\d+))?)?([^?#]*)\??([^#]*)#?(.*)$/
  const match = url.match(regex)
  if (match) {
    return {
      href: url, // 完整的 URL
      protocol: match[1] || '', // 协议部分
      host: match[2] || '', // 主机名和端口
      hostname: match[3] || '', // 域名部分
      port: match[4] || '', // 端口号
      pathname: match[5] || '/', // 路径部分，默认为 '/'
      search: match[6] ? '?' + match[6] : '', // 查询字符串部分
      hash: match[7] ? '#' + match[7] : '', // 片段标识符部分
    }
  }
  return null
}

// parameter type: string|Date 通过返回正/负数/0, 判断比较时间(时间戳值可直接操作,无需通过该方法)
export const compareDate = (a, b) => {
  return (
    (isDate(a) ? a : new Date(a.toString().replace(/-/g, '/'))).getTime() -
    (isDate(b) ? b : new Date(b.toString().replace(/-/g, '/'))).getTime()
  )
}

// 时间格式化
export const dateFormat = (date, format = 'yyyy-MM-dd') => {
  // 字符串时间必须转换横杠,在调试控制台会有warning在部分 iOS 下无法正常使用，iOS 只支持 "yyyy/MM/dd"、"yyyy/MM/dd HH:mm:ss"、"yyyy-MM-dd"、"yyyy-MM-ddTHH:mm:ss"、"yyyy-MM-ddTHH:mm:ss+HH:mm" 的格式...
  const dataObj = new Date(isString(date) ? date.replace(/-/g, '/') : date)
  if (isNaN(dataObj.valueOf())) {
    return date
  }
  const o = {
    'M+': dataObj.getMonth() + 1,
    // month
    'd+': dataObj.getDate(),
    // day
    'h+': dataObj.getHours(),
    // hour
    'm+': dataObj.getMinutes(),
    // minute
    's+': dataObj.getSeconds(),
    // second
    'q+': Math.floor((dataObj.getMonth() + 3) / 3),
    // quarter
    S: dataObj.getMilliseconds(),
    // millisecond
  }
  if (/(y+)/.test(format) || /(Y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (dataObj.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return format
}

// redash
/**
 * Given a delay and a function returns a new function
 * that will only call the source function after delay
 * milliseconds have passed without any invocations.
 *
 * The debounce function comes with a `cancel` method
 * to cancel delayed `func` invocations and a `flush`
 * method to invoke them immediately
 */
export const debounce = ({ delay }, func) => {
  let timer = undefined
  let active = true

  const debounced = (...args) => {
    if (active) {
      clearTimeout(timer)
      timer = setTimeout(() => {
        active && func(...args)
        timer = undefined
      }, delay)
    } else {
      func(...args)
    }
  }
  debounced.isPending = () => {
    return timer !== undefined
  }
  debounced.cancel = () => {
    active = false
  }
  debounced.flush = (...args) => func(...args)

  return debounced
}

/**
 * Given an interval and a function returns a new function
 * that will only call the source function if interval milliseconds
 * have passed since the last invocation
 */
export const throttle = ({ interval }, func) => {
  let ready = true
  let timer = undefined

  const throttled = (...args) => {
    if (!ready) return
    func(...args)
    ready = false
    timer = setTimeout(() => {
      ready = true
      timer = undefined
    }, interval)
  }
  throttled.isThrottled = () => {
    return timer !== undefined
  }
  return throttled
}

/**
 * Like a reduce but does not require an array.
 * Only need a number and will iterate the function
 * as many times as specified.
 *
 * NOTE: This is NOT zero indexed. If you pass count=5
 * you will get 1, 2, 3, 4, 5 iteration in the callback
 * function
 */
export const iterate = (count, func, initValue) => {
  let value = initValue
  for (let i = 1; i <= count; i++) {
    value = func(value, i)
  }
  return value
}

/**
 * Generates a random number between min and max
 */
export const random = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1) + min)
}

/**
 * Generates a unique string with optional special characters.
 */
export const uid = (length = 10, specials) => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789' + specials
  return (
    // '_' + // id或者class不能以数字开头
    iterate(
      length, // length - 1
      (acc) => {
        return acc + characters.charAt(random(0, characters.length - 1))
      },
      ''
    )
  )
}

// allowDigit 为 true, 首位可以是字母或数字, 否则智能为字母
export const generateUUID = (allowDigit = true) => {
  // 用于生成随机16进制字符的辅助函数
  function randomHexDigit() {
    return Math.floor(Math.random() * 16).toString(16)
  }

  // 根据参数决定首位
  const firstChar = allowDigit
    ? randomHexDigit() // 数字或字母
    : String.fromCharCode(97 + Math.floor(Math.random() * 6)) // 字母（a-f）

  // 生成其余部分的 UUID
  const uuid = [
    firstChar, // 首位
    randomHexDigit(),
    randomHexDigit(),
    randomHexDigit(),
    randomHexDigit(),
    '-',
    randomHexDigit(),
    randomHexDigit(),
    randomHexDigit(),
    randomHexDigit(),
    '-',
    '4', // 固定版本号为 4
    randomHexDigit(),
    randomHexDigit(),
    randomHexDigit(),
    '-',
    ((parseInt(randomHexDigit(), 16) & 0x3) | 0x8).toString(16), // variant 设置为 10xx
    randomHexDigit(),
    randomHexDigit(),
    randomHexDigit(),
    '-',
    ...Array.from({ length: 12 }, randomHexDigit),
  ].join('')

  return uuid
}

// function randomUUID() {
//   function randomHex(size) {
//     return Array.from({ length: size }, () =>
//       Math.floor(Math.random() * 256).toString(16).padStart(2, '0')
//     ).join('');
//   }

//   const bytes = randomHex(16);
//   return (
//     bytes.slice(0, 8) + '-' +
//     bytes.slice(8, 12) + '-' +
//     '4' + bytes.slice(13, 16) + '-' + // 设置版本号
//     ((parseInt(bytes[16], 16) & 0x3) | 0x8).toString(16) + bytes.slice(17, 20) + '-' + // 设置variant
//     bytes.slice(20)
//   );
// }

// URLSearchParams polyfill
export function urlSearchParamsUtil(init = '') {
  const params = new Map()

  // 初始化解析
  if (typeof init === 'string') {
    if (init.startsWith('?')) init = init.slice(1)
    const pairs = init.split('&')
    for (const pair of pairs) {
      if (!pair) continue
      const [key, value] = pair.split('=')
      append(params, decodeURIComponent(key), decodeURIComponent(value || ''))
    }
  } else if (typeof init === 'object' && init !== null) {
    for (const key in init) {
      if (Object.prototype.hasOwnProperty.call(init, key)) {
        append(params, key, init[key])
      }
    }
  }

  return function (method, key, value) {
    switch (method) {
      case 'get':
        return get(params, key)
      case 'getAll':
        return getAll(params, key)
      case 'getAllAsObject':
        return getAllAsObject(params)
      case 'append':
        append(params, key, value)
        break
      case 'set':
        set(params, key, value)
        break
      case 'delete':
        params.delete(key)
        break
      case 'has':
        return params.has(key)
      case 'toString':
        return toString(params)
      default:
        throw new Error('Unsupported method')
    }
  }

  function append(params, key, value) {
    if (!params.has(key)) {
      params.set(key, [])
    }
    params.get(key).push(String(value))
  }

  function get(params, key) {
    const values = params.get(key)
    return values ? values[0] : null
  }

  function getAll(params, key) {
    return params.get(key) || []
  }

  function getAllAsObject(params) {
    const result = {}
    for (const [key, values] of params) {
      result[key] = values.length > 1 ? values : values[0]
    }
    return result
  }

  function set(params, key, value) {
    params.set(key, [String(value)])
  }

  function toString(params) {
    const pairs = []
    for (const [key, values] of params) {
      for (const value of values) {
        pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      }
    }
    return pairs.join('&')
  }
}

// export const btoaPolyfill = (bin) => {
//   const b64ch =
//     'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
//   const b64chs = Array.prototype.slice.call(b64ch)
//   let u32,
//     c0,
//     c1,
//     c2,
//     asc = ''
//   const pad = bin.length % 3
//   for (let i = 0; i < bin.length; ) {
//     if (
//       (c0 = bin.charCodeAt(i++)) > 255 ||
//       (c1 = bin.charCodeAt(i++)) > 255 ||
//       (c2 = bin.charCodeAt(i++)) > 255
//     )
//       throw new TypeError('invalid character found')
//     u32 = (c0 << 16) | (c1 << 8) | c2
//     asc +=
//       b64chs[(u32 >> 18) & 63] +
//       b64chs[(u32 >> 12) & 63] +
//       b64chs[(u32 >> 6) & 63] +
//       b64chs[u32 & 63]
//   }
//   return pad ? asc.slice(0, pad - 3) + '==='.substring(pad) : asc
// }

// Strip Markdown stuff from text: https://github.com/zuchka/remove-markdown
export function removeMd(md, options) {
  options = options || {}
  options.listUnicodeChar = options.hasOwnProperty('listUnicodeChar')
    ? options.listUnicodeChar
    : false
  options.stripListLeaders = options.hasOwnProperty('stripListLeaders')
    ? options.stripListLeaders
    : true
  options.gfm = options.hasOwnProperty('gfm') ? options.gfm : true
  options.useImgAltText = options.hasOwnProperty('useImgAltText')
    ? options.useImgAltText
    : true
  options.abbr = options.hasOwnProperty('abbr') ? options.abbr : false
  options.replaceLinksWithURL = options.hasOwnProperty('replaceLinksWithURL')
    ? options.replaceLinksWithURL
    : false
  options.htmlTagsToSkip = options.hasOwnProperty('htmlTagsToSkip')
    ? options.htmlTagsToSkip
    : []
  options.throwError = options.hasOwnProperty('throwError')
    ? options.throwError
    : false

  var output = md || ''

  // Remove horizontal rules (stripListHeaders conflict with this rule, which is why it has been moved to the top)
  // output = output.replace(/^(-\s*?|\*\s*?|_\s*?){3,}\s*/gm, '')
  // 作者原正则除可以匹配水平线(单独在一行上使用三个或更多的星号(***),破折号(---)或下划线(___)), 还会匹配同时显示带粗体和斜体的文本, 如:'***文字***','___文字___','__*文字*__','**_文字_**'或无序列表和粗体'- **文字**'等, 对移除emphasis粗体和斜体造成影响
  // output = output.replace(/^((-\s*?){3,}|(\*\s*?){3,}|(_\s*?){3,})\s*/gm, '') // 相同符号, 但可包含空格, 会额外匹配无序列表和粗体'* **文字**'
  //
  output = output.replace(/^(-{3,}|\*{3,}|_{3,})\s*/gm, '')

  try {
    if (options.stripListLeaders) {
      if (options.listUnicodeChar)
        output = output.replace(
          /^([\s\t]*)([\*\-\+]|\d+\.)\s+/gm,
          options.listUnicodeChar + ' $1'
        )
      else output = output.replace(/^([\s\t]*)([\*\-\+]|\d+\.)\s+/gm, '$1')
    }
    if (options.gfm) {
      output = output
        // Header
        .replace(/\n={2,}/g, '\n')
        // Fenced codeblocks
        .replace(/~{3}.*\n/g, '')
        // Strikethrough
        .replace(/~~/g, '')
        // Fenced codeblocks
        .replace(/`{3}.*\n/g, '')
    }
    if (options.abbr) {
      // Remove abbreviations
      output = output.replace(/\*\[.*\]:.*\n/, '')
    }
    output = output
      // Remove HTML tags
      .replace(/<[^>]*>/g, '')

    var htmlReplaceRegex = new RegExp('<[^>]*>', 'g')
    if (options.htmlTagsToSkip.length > 0) {
      // Using negative lookahead. Eg. (?!sup|sub) will not match 'sup' and 'sub' tags.
      var joinedHtmlTagsToSkip = '(?!' + options.htmlTagsToSkip.join('|') + ')'

      // Adding the lookahead literal with the default regex for html. Eg./<(?!sup|sub)[^>]*>/ig
      htmlReplaceRegex = new RegExp('<' + joinedHtmlTagsToSkip + '[^>]*>', 'ig')
    }

    output = output
      // Remove HTML tags
      .replace(htmlReplaceRegex, '')
      // Remove setext-style headers
      .replace(/^[=\-]{2,}\s*$/g, '')
      // Remove footnotes?
      .replace(/\[\^.+?\](\: .*?$)?/g, '')
      .replace(/\s{0,2}\[.*?\]: .*?$/g, '')
      // Remove images
      .replace(/\!\[(.*?)\][\[\(].*?[\]\)]/g, options.useImgAltText ? '$1' : '')
      // Remove inline links
      .replace(
        /\[([^\]]*?)\][\[\(].*?[\]\)]/g,
        options.replaceLinksWithURL ? '$2' : '$1'
      )
      // Remove blockquotes
      .replace(/^(\n)?\s{0,3}>\s?/gm, '$1')
      // .replace(/(^|\n)\s{0,3}>\s?/g, '\n\n')
      // Remove reference-style links?
      .replace(/^\s{1,2}\[(.*?)\]: (\S+)( ".*?")?\s*$/g, '')
      // Remove atx-style headers
      .replace(
        /^(\n)?\s{0,}#{1,6}\s*( (.+))? +#+$|^(\n)?\s{0,}#{1,6}\s*( (.+))?$/gm,
        '$1$3$4$6'
      )
      // Remove * emphasis
      .replace(/([\*]+)(\S)(.*?\S)??\1/g, '$2$3')
      // Remove _ emphasis. Unlike *, _ emphasis gets rendered only if
      //   1. Either there is a whitespace character before opening _ and after closing _.
      //   2. Or _ is at the start/end of the string.
      .replace(/(^|\W)([_]+)(\S)(.*?\S)??\2($|\W)/g, '$1$3$4$5')
      // Remove code blocks
      .replace(/(`{3,})(.*?)\1/gm, '$2')
      // Remove inline code
      .replace(/`(.+?)`/g, '$1')
      // // Replace two or more newlines with exactly two? Not entirely sure this belongs here...
      // .replace(/\n{2,}/g, '\n\n')
      // // Remove newlines in a paragraph
      // .replace(/(\S+)\n\s*(\S+)/g, '$1 $2')
      // Replace strike through
      .replace(/~(.*?)~/g, '$1')
  } catch (e) {
    if (options.throwError) throw e

    console.error('remove-markdown encountered error: %s', e)
    return md
  }
  return output
}

// 深度拷贝
export function deepAssign(target, ...sources) {
  if (target == null || typeof target !== 'object') {
    throw new TypeError('Target must be a non-null object')
  }

  sources.forEach((source) => {
    if (source == null || typeof source !== 'object') return

    Object.keys(source).forEach((key) => {
      const sourceValue = source[key]
      const targetValue = target[key]

      if (Array.isArray(sourceValue)) {
        // 深拷贝数组
        target[key] = Array.isArray(targetValue) ? targetValue : []
        target[key] = sourceValue.map((item) =>
          typeof item === 'object' && item !== null
            ? deepAssign({}, item)
            : item
        )
      } else if (sourceValue && typeof sourceValue === 'object') {
        // 深拷贝对象
        target[key] =
          targetValue && typeof targetValue === 'object' ? targetValue : {}
        deepAssign(target[key], sourceValue)
      } else {
        // 原始值直接赋值
        target[key] = sourceValue
      }
    })
  })

  return target
}

// 拷贝对象, 支持深拷贝和指定键值拷贝
export const extend = (...args) => {
  let options
  let name
  let src
  let copy
  let copyIsArray
  let clone
  let patch = false // 部分拷贝,只拷贝target对象中存在的键值对象,且拷贝对象第一个有效对象不能为{}
  let target = args[0] || {}
  let i = 1
  const length = args.length
  let deep = false

  if (typeof target === 'boolean') {
    deep = target
    target = args[i] || {}
    i++
  }
  // target=args[1]依然为boolean值,target继续i+1取值
  if (typeof target === 'boolean') {
    patch = target
    target = args[i] || {}
    i++
  }
  if (typeof target !== 'object' && !isFunction(target)) {
    target = {}
  }
  if (i === length) {
    target = this
    i--
  }
  if (isEmptyObject(target) && patch) {
    // 拷贝对象第一项为空对象,且部分拷贝为true,直接返回
    return target
  }
  for (; i < length; i++) {
    if ((options = args[i]) != null) {
      for (name in options) {
        src = target[name]
        copy = options[name]
        if (target === copy) {
          continue
        }
        if (
          deep &&
          (!patch || (patch && src)) &&
          copy &&
          (isObject(copy) || (copyIsArray = Array.isArray(copy)))
        ) {
          // 深复制
          if (copyIsArray) {
            copyIsArray = false
            clone = src && Array.isArray(src) ? src : []
          } else {
            clone = src && isObject(src) ? src : {}
          }
          target[name] = patch
            ? extend(deep, patch, clone, copy)
            : extend(deep, clone, copy)
        } else if (
          (!patch && copy !== undefined) ||
          (patch && src !== undefined && copy !== undefined)
        ) {
          // eslint-disable-line eqeqeq
          target[name] = copy
        }
      }
    }
  }
  return target
}

export function generateSilentPCM16(durationMs = 1000, sampleRate = 16000, channels = 1) {
  // 计算需要的采样点数量
  const samplesPerSecond = sampleRate * channels
  const totalSamples = Math.floor((durationMs / 1000) * samplesPerSecond)
  
  // PCM 16位 = 每个采样点2字节
  const bytesPerSample = 2
  const totalBytes = totalSamples * bytesPerSample
  
  // 创建ArrayBuffer，并用0填充(代表无声音)
  const arrayBuffer = new ArrayBuffer(totalBytes)
  const dataView = new DataView(arrayBuffer)
  
  // 将所有采样点设为0（静音）
  // 注意：PCM 16位使用有符号整数，0代表静音
  for (let i = 0; i < totalSamples; i++) {
    const byteOffset = i * bytesPerSample
    dataView.setInt16(byteOffset, 0, true) // true表示小端序
  }
  
  return arrayBuffer
}

/**
 * 生成PCM 16位格式的空音频数据 (Uint8Array格式)
 * @param {number} durationMs - 音频时长(毫秒)
 * @param {number} sampleRate - 采样率，默认16000Hz
 * @param {number} channels - 声道数，默认1(单声道)
 * @returns {Uint8Array} PCM 16位格式的空音频数据
 */
export function generateSilentPCM16Bytes(durationMs = 1000, sampleRate = 16000, channels = 1) {
  const arrayBuffer = generateSilentPCM16(durationMs, sampleRate, channels)
  return new Uint8Array(arrayBuffer)
}
