export class RequestTask {
  constructor() {
    this.tasksMap = new Map()
  }

  getTasks<PERSON>y<PERSON><PERSON>(key) {
    return this.tasksMap.get(key)
  }

  addTask<PERSON>y<PERSON><PERSON>(key, task) {
    if (!this.tasksMap.has(key)) {
      const value = new Set()
      value.add(task)
      this.tasksMap.set(key, value)
      return
    }
    const values = this.tasksMap.get(key)
    values.add(task)
  }

  add(key, task) {
    this.addTaskBy<PERSON>ey(key, task)
  }

  removeTask<PERSON>y<PERSON><PERSON>(key, task) {
    const tasks = this.tasksMap.get(key)
    if (tasks) {
      tasks.delete(task)
    }
  }

  removeAllBy<PERSON>ey(key) {
    this.tasksMap.delete(key)
  }

  clear() {
    this.tasksMap.clear()
  }

  // 通过页面key， 终止掉所有其他页面的未完成请求
  abortOthersByKey(key) {
    if (this.tasksMap.size) {
      this.tasksMap.forEach((values, taskKey) => {
        if (taskKey !== key) {
          if (values.size) {
            values.forEach(task => {
              if (task && task.abort) {
                task.abort()
              }
            })
            this.removeAll<PERSON><PERSON><PERSON><PERSON>(taskKey)
          }
        }
      })
    }
  }
}
