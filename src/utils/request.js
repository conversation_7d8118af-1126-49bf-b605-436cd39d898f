import { RequestTask } from './requestTask'
import { baseURL, wxAppid } from '@/assets/config'
import { useStore } from '@/stores/mainStore'
import { deepMerge, isAbsoluteURL, parseURL } from '@/utils'

export function request(options = {}) {
  const { canAbort } = options
  const app = getApp()
  const currentPage = getCurrentPages().at(-1)?.route
  return new Promise((resolve, reject) => {
    let requestTask = null
    
    // 如果请求在切换页面前已经完成了, 在requestTasks中移除
    const removeAbortTask = () => {
      // 这里只对配置了canAbort 的请求做特殊逻辑
      if (canAbort && requestTask) {
        if (app.globalData.requestTasks) {
          app.globalData.requestTasks.removeTaskByKey(currentPage, requestTask)
        }
      }
    }
    
    requestTask = wx.request({
      ...options,
      success(res) {
        removeAbortTask()
        resolve(res)
      },
      fail(err) {
        removeAbortTask()
        // if (canAbort && err && err.errMsg === 'request:fail abort') {
        //   // 终止了请求会进入这个逻辑，一般不用特殊处理，直接return掉
        //   // 接口请求手动终止, 逻辑自定义
        // }
        console.log('fail err', err)
        reject(err)
      },
    })
    
    // 将配置了允许中断的请求加入到requestTasks中
    if (canAbort) {
      if (!app.globalData.requestTasks) {
        app.globalData.requestTasks = new RequestTask()
      }
      app.globalData.requestTasks.addTaskByKey(currentPage, requestTask)
    }
  })
}

let loadingCount = 0

// 封装请求添加拦截,主动拼入token,处理code返回
export function createRequest(defaults = {}) {
  async function _request(configOrUrl, config) {
    if (typeof configOrUrl === 'string') {
      config = config || {}
      config.url = configOrUrl
    } else {
      config = configOrUrl || {}
    }

    if (!isAbsoluteURL(config.url) && defaults.baseURL) {
      config.url = defaults.baseURL + config.url
    }

    const urlProps = parseURL(config.url)
    const pathnames = urlProps?.pathname.split('/').filter(Boolean)
    config.url = `${urlProps?.protocol}//${urlProps?.host}/${pathnames?.slice(1).join('/')}${urlProps?.search}${urlProps?.hash}`


    if (config.loading) {
      uni.showLoading({
        mask: true,
      })
      loadingCount++
    }
    // console.log(urlProps)
    const store = useStore()
    return new Promise((resolve, reject) => {
      request(
        deepMerge(
          defaults,
          config.mixinAuth === false
            ? {}
            : {
              header: {
                Issuer: wxAppid,
                Authorization: store.userinfo?.token,
                _R_: pathnames?.at(0)?.toUpperCase(),
              },
            },
          config
        )
      )
        .then(({ data }) => {
          config.intercept === false || [0, '0'].includes(data?.code)
            ? resolve(data)
            : reject({ errMsg: data.msg, errno: data.code, data }) // 和小程序fail回调参数统一
        })
        .catch((res) => {
          // 这里未对取消请求做处理
          reject(res)
          // 或可再增加一层拦截统一处理错误信息
        })
        .finally(() => {
          if (config.loading) {
            if (loadingCount > 0) {
              loadingCount--
            }
            if (loadingCount < 1) {
              uni.hideLoading()
            }
          }
        })
    })
  }
  const instance = {}
    ;['get', 'post'].forEach(function forEachMethod(method) {
      instance[method] = function (url, config) {
        return _request(
          deepMerge(
            // https://developers.weixin.qq.com/miniprogram/dev/api/network/request/wx.request.html content-type 默认为 application/json
            // {
            //   header:
            //     method === 'post'
            //       ? {
            //           'content-type': defaults?.header?.['content-type']
            //             ? defaults.header['content-type']
            //             : 'application/json',
            //         }
            //       : {},
            // },
            config || {},
            {
              method: method.toUpperCase(),
              url,
            }
          )
        )
      }
    })
  return instance
}

export const primaryRequest = createRequest({
  baseURL,
  // baseURL: import.meta.env.VITE_BASE_URL,
})

export const testRequest = createRequest({
  baseURL: 'https://m-daily.xingqi.work',
  // baseURL: import.meta.env.VITE_BASE_URL,
})