<template>
  <view class="startup-page">
    <MaskLayer :show="show" />
  </view>
</template>
<script setup>
  import { ref, reactive } from 'vue'
  import { onShow, onLoad } from '@dcloudio/uni-app'
  import { wechatAuth } from '@/api'
  import MaskLayer from '@/components/MaskLayer'
  import { useStore } from '@/stores/mainStore'

  const app = getApp()
  const store = useStore()
  const show = ref(true)
  const shareInfo = reactive({})

  // console.log('getSystemInfoSync:', uni.$u.sys())

  onShow(() => {
    uni.login({
      success: res => {
        if (res.code) {
          wechatAuth({
            data: {
              jsCode: res.code
            },
          }).then((res2) => {
            console.log('AuthInfo:', res2)
            store.updateUserinfo(res2.data)
            uni.redirectTo({
              url: app.globalData.extraData?.simbotId ? '/pages/aiChat/aiChat' : '/pagesA/myCenter/myCenter'
            })
          }).catch(err => {
            console.log('获取用户登录状态信息失败:', err)
            if (err.errno === '4001') {
              uni.redirectTo({
                url: app.globalData.simbotId ? '/pages/aiChat/aiChat' : '/pages/register/register'
              })
            }
          })
        } else {
          console.error('wx.login获取登录凭证(code)失败:', res)
        }
      },
      fail: err => {
        console.error('wx.login获取登录凭证(code)失败:', err)
        uni.showToast({
          title: `${err.errno}:${err.errMsg}`,
          icon: 'none',
        })
      },
    })
  })

  onLoad((options) => {
    console.log('index onLoad:', options)
  })

</script>
<style lang="scss" scoped>
  .startup-page {
    // height: 100vh;
    // overflow: hidden;
  }
</style>
