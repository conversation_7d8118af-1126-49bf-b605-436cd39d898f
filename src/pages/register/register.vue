<template>
  <view class="auth-list">
    <!-- <view class="field-item mb-10">
      <button open-type="chooseAvatar" class="choose-avatar"
        @chooseavatar="onChooseavatar">
        <image :src="avatarUrl" mode="aspectFill"></image>
      </button>
    </view> -->
    <view class="field-item border-bottom">
      <view class="me-4 w-14">
        <text>昵称</text>
      </view>
      <!-- 昵称需添加隐私授权 -->
      <view class="flex-1" @touchstart.stop="handleTouchInput">
        <input class="nickname" type="nickname" :maxlength="20" :focus="focused"
          @blur="blurHandle" placeholder="点击输入框，输入昵称" />
      </view>
    </view>
    <view class="field-item border-bottom mb-14">
      <view class="me-4 w-14">
        <text>手机号</text>
      </view>
      <!-- <input :value="mobileCode" hidden /> -->
      <button class="phone-number" open-type="getPhoneNumber"
        @getphonenumber="getPhoneNumber" @touchstart="phoneNumberClick">
        {{ authData.mobileCode ? '1**********' : '手机号码' }}
      </button>
    </view>
    <view>
      <!-- <up-button class="!w-3/4" :disabled="invalid" @click.stop="saveHandle"
        type="primary" shape="circle" text="完成"></up-button> -->
    </view>
  </view>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { useStore } from '@/stores/mainStore'
  import { onLoad } from '@dcloudio/uni-app'
  import { registerApi } from '@/api'
  import { wxAppid, baseURL } from '@/assets/config'

  const app = getApp()
  const store = useStore()
  console.log(store.userinfo)
  const avatarUrl = ref('../../static/media/avatar.jpg')
  const focused = ref(false)
  const authData = reactive({
    avatar: '',
    nickname: '',
    mobileCode: '',
  })

  onLoad((options) => {
    // wx.getUserInfo({
    //   success: (res) => {
    //     console.log('getUserInfo', res)
    //   },
    // })
    // wx.onNeedPrivacyAuthorization(resolve => {
    //   // 需要用户同意隐私授权时
    //   // 弹出开发者自定义的隐私授权弹窗
    //   console.log('onNeedPrivacyAuthorization', resolve)
    //   resolve({ buttonId: 'agree-btn'， event:'agree' })
    // })
  })

  const invalid = computed(() => {
    console.log(authData)
    return !authData.nickname || !authData.mobileCode // || !authData.avatar
  })

  // 获取头像
  const onChooseavatar = (e) => {
    const avatar = e.detail.avatarUrl
    avatarUrl.value = avatar
    authData.avatar = avatar

    console.log(avatar)
    // uni.showLoading()
    // uni.uploadFile({
    //   url: baseURL + '/sso/tinyapp/wechat/uploadAvatar',
    //   filePath: avatar,
    //   name: 'file',
    //   // formData: {},
    //   header: {
    //     Issuer: wxAppid,
    //     Authorization: store.userinfo?.token,
    //   },
    //   success(res) {
    //     const data = res.data
    //     console.log(res)
    //     uni.hideLoading()
    //   },
    //   fail: (err) => {
    //     console.log('头像上传错误:', err)
    //     uni.showToast({
    //       icon: 'error',
    //       title: '头像上传失败'
    //     })
    //   },
    //   // complete: () => {
    //   //   uni.hideLoading()
    //   // }
    // })
  }

  const handleTouchInput = () => {
    // console.log('handleTouchInput')
    focused.value = false // 必须否则首次输入后输入框无法再获得焦点
    if (uni.requirePrivacyAuthorize) {
      uni.requirePrivacyAuthorize({
        success: (res) => {
          console.log('用户同意了隐私协议 或 无需用户同意隐私协议')
          // 用户同意隐私协议后给昵称input聚焦
          focused.value = true
        },
        fail: (res) => {
          console.log('用户拒绝了隐私协议')
        },
      })
    } else {
      focused.value = true
    }
  }

  const blurHandle = (e) => {
    authData.nickname = e.detail.value.trim()
  }

  const getPhoneNumber = (e) => {
    console.log('getPhoneNumber', e)
    authData.mobileCode = e.detail.code
    // 通过动态令牌换取用户手机号
  }

  // 用户可能拒绝官方隐私授权弹窗，为了避免过度弹窗打扰用户，开发者再次调用隐私相关接口时，若距上次用户拒绝不足10秒，将不再触发弹窗，直接给到开发者用户拒绝隐私授权弹窗的报错 https://developers.weixin.qq.com/miniprogram/dev/framework/user-privacy/PrivacyAuthorize.html#%E5%85%AD%E3%80%81%E5%AE%98%E6%96%B9%E9%9A%90%E7%A7%81%E5%BC%B9%E7%AA%97%E5%8A%9F%E8%83%BD%E8%AF%B4%E6%98%8E
  const phoneNumberClick = (e) => {
    console.log('phoneNumberTouchstart', e)
    uni.getPrivacySetting({
      success: (res) => {
        console.log('查询隐私授权:', res)
        if (res.needAuthorization) {
          uni.requirePrivacyAuthorize({
            success: () => {
              console.log('用户同意了隐私协议 或 无需用户同意隐私协议')
              // 用户同意隐私协议后给昵称input聚焦
            },
            fail: (res) => {
              console.log('用户拒绝了隐私协议')
            },
          })
          e.stopPropagation()
        }
      },
    })
  }

  // 保存
  const saveHandle = () => {
    uni.login({
      success: (res) => {
        if (res.code) {
          registerApi({
            data: { ...authData, jsCode: res.code },
            loading: true,
          })
            .then((res) => {
              store.updateUserinfo(res.data)
              uni.showToast({
                icon: 'success',
                title: '注册成功',
                mask: true,
              })
              setTimeout(() => {
                uni.redirectTo({
                  url: app.globalData.extraData?.simbotId
                    ? '/pages/aiChat/aiChat'
                    : '/pagesA/myCenter/myCenter',
                })
              }, 1200)
            })
            .catch((err) => {
              console.log('用户授权注册错误:', err)
              uni.showToast({
                icon: 'error',
                title: '注册失败',
              })
            })
        } else {
          console.error('wx.login获取登录凭证(code)失败:', res)
          uni.showToast({
            icon: 'error',
            title: '注册失败',
          })
        }
      },
      fail: (err) => {
        console.error('wx.login获取登录凭证(code)失败:', err)
        uni.showToast({
          title: `${err.errno}:${err.errMsg}`,
          icon: 'none',
        })
      },
    })
  }
</script>

<style lang="scss">
  .auth-list {
    padding-top: 120rpx;
    color: #b5b7c8;
    font-size: 28rpx;

    .field-item {
      display: flex;
      align-items: center;
      padding-inline: 64rpx;

      view:first-child text {
        position: relative;

        &::after {
          content: '*';
          color: $uni-color-error;
          padding-left: 8rpx;
        }
      }

      .input-placeholder {
        color: #9a9cae;
      }
    }

    .border-bottom {
      border-bottom: 1px solid #333 !important;
    }

    .choose-avatar {
      padding-inline: 0;

      &::after {
        content: none;
      }

      image {
        display: block;
        width: 120rpx;
        height: 120rpx;
      }
    }

    .nickname,
    .phone-number {
      flex: 1;
      padding: 28rpx 0;
      font-size: 28rpx;
      background-color: #212529;
      color: #9a9cae;

      // &.focus {
      //   background-color: #26272f;
      // }
    }

    .phone-number {
      line-height: 1.4rem;
      text-align: left;
    }
  }
</style>
