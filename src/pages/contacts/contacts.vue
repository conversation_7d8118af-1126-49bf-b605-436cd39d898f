<template>
  <view class="bg-page visitor">
    <up-navbar class="mb-4" title="联系人" :border="false" placeholder
      bgColor="transparent" :titleStyle="{ color: '#fff;' }" leftIconColor="#fff">
      <template #left>
        <up-button class="!h-[52rpx] !w-auto !border-0" size="small"
          shape="circle" @click="handleMyCenter">
          <!-- <up-icon class="me-0.5" name="users" custom-prefix="iconfont"
              size="16"></up-icon> -->
          <image class="w-3.5 h-3.5 mr-0.5" src="/static/media/myCenter.png"
            mode="scaleToFill" />
          我的
        </up-button>
      </template>
    </up-navbar>
    <view class="visitor-list">
      <scroll-view v-if="scrollViewHeight"
        :style="{ height: scrollViewHeight + 'px' }" scroll-y
        @scrolltolower="onScrollToLower">
        <view id="scrollView" class="px-2">
          <view class="visitor-list-item" v-for="item in contactList"
            :key="item.simbotId">
            <view class="image-item">
              <view class="image-wrap">
                <image :src="item.avatar" mode="widthFix" />
              </view>
              <!-- <view class="badge"></view> -->
            </view>
            <view class="visitor-info overflow-hidden">
              <view>{{ item.name }}</view>
              <view class="my-1">{{ item.job }}</view>
              <view>{{ item.company }}</view>
            </view>
            <view class="chatBtn-wrap">
              <up-button class="chatBtn" type="primary" shape="circle"
                size="small" text="去聊天"
                @click="handleGoChat(item.simbotId)"></up-button>
            </view>
          </view>
          <view class="text-center mt-3" v-if="loading">加载中...</view>
          <view class="text-center my-16 text-white"
            v-if="requested && !contactList?.length">
            暂无访问记录
          </view>
          <view class="text-center pt-5 text-gray-400"
            v-if="contactList?.length && !hasMoreContacts && scrollToLower">
            没有更多了...
          </view>
        </view>
      </scroll-view>
    </view>
    <LoginModal />
  </view>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import { getContacts } from '@/api'
  import LoginModal from '@/components/LoginModal.vue'
  import { useStore } from '@/stores/mainStore'
  const store = useStore()
  const app = getApp()
  const systemInfo = uni.$u.sys()
  const contactList = reactive([])
  const pageNumber = ref(1)
  const scrollViewHeight = ref(0)
  const loading = ref(false)
  const hasMoreContacts = ref(true)
  const scrollToLower = ref(false)
  const requested = ref(false)
  const areaInsetsHeight = ref(Math.max(systemInfo.safeAreaInsets.bottom, 16))

  onLoad(() => {
    // console.log(systemInfo)
    scrollViewHeight.value =
      systemInfo.windowHeight - systemInfo.statusBarHeight - areaInsetsHeight.value - 44 - 16 // 44 uview自定义导航栏默认高度, 16为导航下边距

    app.globalData.loginPromise.finally(() => {
      const { token, expires } = store.userinfo
      if (!token || (token && +new Date() > expires)) {
        // 需要登录
        return
      }
      getContactsHandle()
    })
  })

  const onScrollToLower = () => {
    scrollToLower.value = true
    if (!loading.value && hasMoreContacts.value) {
      getContactsHandle()
    }
  }

  const getContactsHandle = () => {
    loading.value = true

    getContacts({
      data: {
        page: pageNumber.value,
        size: 10,
      },
    })
      .then(({ data, page: { total, size } }) => {
        console.log('获取联系人成功', data, total, size)

        contactList.push(...data)

        if (total > size) {
          pageNumber.value++
        } else {
          hasMoreContacts.value = false
        }
      })
      .catch((err) => {
        console.log('获取联系人失败', err)
        uni.showToast({
          title: '获取联系人失败',
          icon: 'error',
        })
      }).finally(() => {
        loading.value = false
        requested.value = true
      })
  }

  const handleGoChat = (simbotId) => {
    uni.navigateTo({
      url: '/pages/aiChat/aiChat?simbotId=' + simbotId
    })
  }
  const handleMyCenter = () => {
    uni.navigateTo({
      url: '/pagesA/myCenter/myCenter'
    })
  }
</script>

<style lang="scss" scoped>
  .visitor {
    min-height: 100vh;
    // color: #fff;
    font-size: 28rpx;

    .myCenter {
      display: flex;
      align-items: center;
      color: #071437;
      background: #FFFFFF;
      border-radius: 14px;
      padding: 6px 10px;

      image {
        width: 16px;
        height: 16px;
        margin-right: 6px
      }
    }

    .visitor-list {

      .visitor-list-item {
        display: flex;
        margin-bottom: 8px;
        padding: 8px 0px 8px 0px;
        background: url("../../static//media/contact_bg.png") no-repeat;
        background-size: 100% 100%;

        .image-item {
          align-self: center;
          margin-left: 8px;
        }

        &>view {
          &:first-child {
            position: relative;
          }

          .badge {
            position: absolute;
            top: 0;
            right: 0;
            width: 40rpx;
            height: 40rpx;
            background: url('../../static/media/xq.png') center/contain no-repeat;
          }

          // &:last-child {
          //   align-self: center;
          //   width: 140rpx;
          //   background: url("../../static/media/chatBtnBg.png") center/contain no-repeat;
          // }
        }

        .image-wrap {
          height: 160rpx;
          // border: 1px solid #333;
          border-radius: 50%;
          overflow: hidden;

          &,
          image {
            width: 160rpx;
          }

          image {
            height: 276rpx;
          }
        }



        .visitor-info {
          flex: 1;
          margin-left: 32rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;

          &>view {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 14px;
            color: #080642;

            &:first-child {
              font-size: 34rpx;
              // margin-bottom: 20rpx;
              font-size: 18px;
              font-weight: 600;
            }

            &:last-child {
              // margin-bottom: 20rpx;
              font-size: 14px;
              color: #6080A6;
            }
          }
        }

        .chatBtn-wrap {
          width: 90px;
          background: url("../../static/media/chatBtnBg.png") no-repeat;
          background-size: contain;
          padding: 0 10px;
          display: flex;
          align-items: center;
          margin-right: 10px;

          .chatBtn {
            background: linear-gradient(270deg, #5078FF 0%, #94F0FF 100%) !important;
            border-radius: 15px !important;
            border: none !important;
          }
        }


      }
    }
  }
</style>
