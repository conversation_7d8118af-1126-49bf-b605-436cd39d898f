<template>
  <!-- <image class="bg-mask w-[560rpx] h-[560rpx]" src="https://cdn-daily.xingqi.work/img/loading.png" /> -->
  <view :class="theme=='light'?'':'bg-[#252627]'" class="w-full h-full">
  <image class="phone-call-ripple w-[1000rpx] h-[1000rpx]" :style="{top: `${shareHeight - 50}px`}" src="https://cdn.xingqi.work/img/ripple.gif" />
  <view class="phone-call-wrapper">
    <up-navbar :border="false" placeholder bgColor="transparent" titleWidth="240rpx" title="农商行"
      :autoBack="false" :titleStyle="{ color: '#fff;' }" leftIconColor="#fff" @leftClick="handleClose"></up-navbar>
    <view class="w-[476rpx] h-[476rpx] rounded-full m-auto mt-[70rpx]" style="overflow: hidden;border: 1rpx solid white;">
      <image class="w-[476rpx] h-[476rpx] rounded-full m-auto" :src="initData.vivid.image.url" mode="widthFix"></image>
    </view>
    <!-- 顶部聊天框 -->
    <view class="absolute left-4 right-4 max-h-[320rpx] overflow-hidden chat-container" :class="`top-[${shareHeight}px]`">
      <!-- 聊天记录 -->
      <scroll-view class="scroll-container px-4 py-3 max-h-[320rpx]" scroll-y :scroll-into-view="scrollToViewId" style="width: 620rpx;">
        <template v-for="(message, index) in chatRecords" :key="index">
          <!-- 用户消息 -->
          <view v-if="message.type === 'question'" :id="message.id" class="mb-4">
            <view class="text-[32rpx] text-[#6A6A6A]">
              {{ message.content }}
            </view>
            <image v-if="!chatRecords[index + 1].content && index + 3 > chatRecords.length" class="w-[60rpx] h-3"
              src="../../static/media/phoneMaskRobot.png" mode="aspectFit" style="width: 40rpx;height: 40rpx;" />
          </view>
          <!-- 数字人消息 -->
          <view v-else :id="message.id" class="mb-4">
            <view class="text-[32rpx] text-[#fff]">
              {{ message.content }}
            </view>
          </view>
        </template>
        <!-- 底部滚动锚点 -->
        <view id="chat-bottom" class="h-[1rpx] mt-[10rpx]"></view>
      </scroll-view>

      <!-- 状态提示 -->
      <view class="px-4 py-2 text-xs text-[#6F6F6F]">
        <view v-show="(!isAsrReady || !isRecordReady) && !chatRecords.length">请稍等...</view>
        <view
          class="text-[28rpx] text-white"
          v-show="isAsrReady && isRecordReady && !isRecognition && !isMuted && !isPlayRecord && !chatRecords.length">
          你可以开始说话</view>
        <view v-show="isAsrReady && isRecordReady && isRecognition && !isMuted && !isPlayRecord && !chatRecords.length">
          正在聆听...</view>
        <view v-show="isAsrReady && isRecordReady && isMuted && !chatRecords.length">已静音</view>
      </view>
    </view>

    <!-- 底部固定状态和按钮 -->
    <view class="absolute bottom-[112rpx] left-0 right-0">
      <AsrRecognition ref="AsrRecognitionRef" @recognition-end="recognitionResult" @audio-close="audioClose"
        @changeRecognitionStatus="handleChangeStatus">
      </AsrRecognition>
      <!-- 左右按钮布局 -->
      <view class="flex justify-center items-center gap-[174rpx]">
        <!-- 左边按钮 - handleClose -->
        <view
          class="flex items-center justify-center w-[152rpx] h-[152rpx] rounded-full bg-[rgba(198,200,218,0.34)] cursor-pointer transition-opacity duration-200 hover:opacity-80"
          @click="handleClose">
          <image src="/static/media/phoneMaskLeft.png" class="w-12 h-12" />
        </view>

        <!-- 右边按钮 - handleTouchStart -->
        <view
          class="flex items-center justify-center w-[152rpx] h-[152rpx] rounded-full bg-[rgba(198,200,218,0.34)] cursor-pointer transition-opacity duration-200 hover:opacity-80"
          @click="changeMute">
          <image :src="muteButtonIcon" class="w-12 h-12" />
        </view>
      </view>
    </view>
  </view>
  </view>
</template>

<script>
import AsrRecognition from '@/components/AsrRecognition.vue'
import XFTextToSpeech from '../../components/XunFeiTextToSpeech'
import PublicTextToSpeech from '../../components/PublicTextToSpeech'
import CosyVoiceTextToSpeech from '../../components/CosyVoiceTextToSpeech'
import AudioPlayer from '../../components/AudioPlayer'
import { TextDecoder } from '@kayahr/text-encoding'
import { useStore } from '@/stores/mainStore'
import { wxAppid } from '@/assets/config'
import { uid, removeMd } from '@/utils'
import {
  getChatInit,
  aiChatURL,
  askingChunkedURL,
  initPerson
} from '@/api'
let tts = null
let chatTask = null
let askingTask = null
let personInitPromise = null
const store = useStore()
const player = new AudioPlayer()

export default {
  data() {
    return {
      shareHeight: uni.$u.sys().safeArea.top,
      isMuted: false, // 禁音状态
      chatRecords: [], // 聊天列表
      scrollToViewId: '', // 滚动到的视图ID
      chunkReceiving: false, // 问题回复-分块接收中
      askingChunkReceiving: false, // 猜你想问-分块接收中
      initData: getApp().initData, // 初始化数据,用于存放阿里云appkey等
      // initData: {
      //   voice: {
      //     code: "sicheng",
      //     expires: 1757680005,
      //     platform: "AliyunVoicePublic",
      //     projectKey: "cGWmNpUlN1f0oa5S",
      //     token: "882e92c118934d4cbeddd5eecd5fb058",
      //     url: "https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/tts"
      //   }
      // },
      soundOff: false, // 语音播放关闭
      playing: false, // 机器人语音播放中
      audioQueue: [], // 合成音频的播放队列
      simbotId: '', // 机器人Id
      isAsrReady: false,
      isRecordReady: false,
      isRecognition: false,
      isMuted: false,
      isPlayRecord: false
    }
  },
  components: { AsrRecognition },
  computed: {
    muteButtonIcon() {
      return this.isMuted ? '/static/media/phonecallVioceBan.png' : '/static/media/phonecallVioce.png'
    }
  },
  onLoad(query) {
    // console.log('开始数据展示:' + query.simbotId)
    this.simbotId = query.simbotId;
  },
  mounted() {
    this.extraData = getApp().globalData?.extraData
    this.initTTS();

    // 播放状态
    player.onPlay((res) => {
      console.log('语音播放')
      this.playing = true
      this.$refs.AsrRecognitionRef.changePlay(true);
    })

    player.onStop(() => {
      this.playing = false
      if (!player.audioQueue.length) {
        this.$refs.AsrRecognitionRef.changePlay(false);
      }
    })
  },
  onUnload() {
    // 停止合成
    tts.stopSynthesis()
    this.playing = false
    this.audioQueue = [];
    player.stop()
  },
  methods: {
    // 修改状态
    handleChangeStatus(event) {
      this.isAsrReady = event.isAsrReady;
      this.isRecordReady = event.isRecordReady;
      this.isRecognition = event.isRecognition;
      this.isMuted = event.isMuted;
    },
    // 滚动到最新消息
    scrollToLatest() {
      this.$nextTick(() => {
        if (this.chatRecords.length > 0) {
          const latestMessage = this.chatRecords.at(-1)
          this.scrollToViewId = latestMessage.id
        }
      })
    },
    // 滚动到底部（用于数字人消息渲染时）
    scrollToBottom() {
      this.$nextTick(() => {
        // 先清空scrollToViewId，再设置新值，确保每次都能触发滚动
        this.scrollToViewId = ''
        this.$nextTick(() => {
          this.scrollToViewId = 'chat-bottom'
        })
      })
    },
    // 回复chunked消息拼接 语音合成
    messageSplicing(message) {
      const lastRecord = this.chatRecords.at(-1)
      lastRecord.content += message

      // 数字人消息更新时滚动到底部
      this.scrollToBottom()

      // 未设置声音voice返回null, voice:{platform: AliyunCosyVoiceTrain|AliyunVoicePublic}
      const { voice } = this.initData
      if (voice && !this.soundOff) {
        const plainMsg = removeMd(message)
        console.log(
          'plainMsg:',
          plainMsg.replace(/\n/g, '↵'),
          plainMsg.length
        )
        tts.speechSynthesis(plainMsg)
      }
    },
    // 识别语音的问题
    recognitionResult(res) {
      console.log('识别数据:' + res)
      this.sendMessageHandle(res);
    },
    // 返回
    handleClose() {
      uni.navigateBack()
      // this.sendMessageHandle('银行卡办理')
    },
    // 改变禁音状态
    changeMute() {
      this.isMuted = !this.isMuted
      this.$refs.AsrRecognitionRef?.changeMuted()
    },

    // 关闭语音播报
    audioClose() {
      // 停止合成
      tts.stopSynthesis()
      this.playing = false
      player.stop()
      this.$refs.AsrRecognitionRef.changePlay(false);
    },

    // tts初始化
    initTTS() {
      const { url, projectKey, token, code, platform } =
        this.initData.voice
      switch (platform) {
        // 讯飞语音合成接口鉴权, 服务端会对date进行时钟偏移检查(300s), 超出偏差的请求都将被拒绝 https://www.xfyun.cn/doc/tts/online_tts/API.html#%E6%8E%A5%E5%8F%A3%E8%B0%83%E7%94%A8%E6%B5%81%E7%A8%8B
        case 'XunFeiOneSentence':
          tts = new XFTextToSpeech(url, {
            header: {
              app_id: projectKey,
              res_id: code,
            },
          })

          // 每隔5分钟重新获取请求链接
          setInterval(() => {
            getChatInit({
              data: {
                simbotId: this.simbotId,
              },
            })
              .then((res) => {
                tts.url = res.data.voice.url
                // xftts.updateURL(res.data.voice.url)
              })
              .catch((err) => {
                console.log('讯飞语音合成刷新URL失败:', err)
              })
          }, 300000)
          break
        case 'AliyunCosyVoiceTrain':
          tts = new CosyVoiceTextToSpeech(url, {
            appkey: projectKey,
            token,
            voice: code,
          })
          break
        // 公共声音/个性化声音
        // case 'AliyunVoicePublic':
        // case 'AliyunMeasureTrain':
        default:
          tts = new PublicTextToSpeech(url, {
            appkey: projectKey,
            token,
            voice: code,
          })
      }

      tts.onSynthesisCompleted((filePath, text) => {
        player.addAudio(filePath)
      })

      // 欢迎语
      // this.chatRecords = [
      //   {
      //     // type: 'greetings',
      //     type: 'answer',
      //     id: '_' + uid(),
      //     content: this.initData.welcome,
      //     // dateTime: dateFormat(new Date(), 'hh:mm:ss'),
      //     dateTime: new Date(),
      //   },
      // ]

      // 大模型初始化
      personInitPromise = new Promise((resolve, reject) => {
        initPerson({
          data: {
            token: this.initData.chat?.token,
          },
        })
          .then((err) => {
            console.log('大模型初始化完成', err)
            resolve()
          })
          .catch((err) => {
            console.log('大模型初始化失败:', err)
            uni.showToast({
              icon: 'error',
              title: '大模型初始化失败',
            })
            reject()
          })
      })
    },

    // 发消息
    async sendMessageHandle(message) {
      this.chatRecords.push(
        {
          type: 'question',
          id: '_' + uid(), // id或者class不能以数字开头
          content: message,
          // dateTime: dateFormat(new Date(), 'hh:mm:ss'),
          dateTime: new Date(),
        },
        {
          type: 'answer',
          id: '_' + uid(),
          content: '',
          // dateTime: dateFormat(+new Date() + 1000, 'hh:mm:ss'),
          dateTime: new Date(),
        }
      )

      // 滚动到最新添加的用户消息
      this.scrollToLatest()

      this.chunkReceiving = true
      // this.askingChunkReceiving = true // 一定先于聊天请求返回是否需要??
      console.log('chunkReceiving:', true)

      // 大模型初始化完成才可以发送消息
      personInitPromise.then(async () => {
        // 确保上一轮语音合成完成
        if (
          ['XunFeiOneSentence', 'AliyunCosyVoiceTrain'].includes(
            this.initData?.voice?.platform
          )
        ) {
          await tts.synthesizedPromise()
          console.log('all Synthesized')
        }

        // 欢迎语在合成中,发送消息需取消合成中所有请求,requestTask中无法取消具体请求
        // aiChat({
        //   data: {
        //     message,
        //     token: this.initData?.chat?.token,
        //     ref: true
        //   },
        //   enableChunked: true, // 开启 transfer-encoding chunked
        //   intercept: false,
        //   canAbort: true,
        // })
        //   .then(() => {
        //     // 这里是成功调用完的 可以写去掉加载中等逻辑
        //     // 由于SSE特性,需要由用户端断开连接,所以在使用完毕时,需要调用requestTask.abort()断开连接
        //     chatTask.abort()
        //   })
        //   .catch((err) => {
        //     console.log('聊天接口错误', err)
        //     uni.showToast({
        //       title: '消息获取失败',
        //       icon: 'error',
        //     })
        //   })
        //   .finally(() => {
        //     this.chunkReceiving = false
        //     console.log('finally chunkReceiving:', this.chunkReceiving)
        //   })

        // chatTask = app.globalData.requestTasks
        //   .getTasksByKey(getCurrentPages().at(-1).route)
        //   ?.values()
        //   ?.next()?.value

        if (!this.soundOff) {
          console.log('语音播报状态:' + this.soundOff)
          // 恢复合成/播放
          tts.startSynthesis()
          player.start()
        }

        const rid = uid(32)

        // 微信升级到指定版本后,不支持并发enableChunked请求
        Promise.all([
          new Promise((resolve, reject) => {
            chatTask = wx.request({
              url: aiChatURL,
              method: 'post',
              enableChunked: true, // 开启 transfer-encoding chunked
              data: {
                message,
                token: this.initData?.chat?.token,
                ref: this.extraData.source === 'true' ? 1 : 0,
                rid,
              },
              header: {
                Issuer: wxAppid,
                Authorization: store.userinfo?.token,
                _R_: 'ICHAT',
              },
              success: (res) => {
                // 这里是成功调用完的 可以写去掉加载中等逻辑
                // 由于SSE特性,需要由用户端断开连接,所以在使用完毕时,需要调用requestTask.abort()断开连接
                chatTask.abort()

                // if (res.statusCode === 200) {}
                resolve(res)
              },
              fail: (err) => {
                console.log('聊天接口错误', err)
                if (err.errMsg !== 'request:fail abort') {
                  uni.showToast({
                    title: '消息获取失败',
                    icon: 'error',
                  })
                }
                reject(err)
              },
              complete: () => {
                this.chunkReceiving = false
                console.log('finally chunkReceiving:', this.chunkReceiving)
              },
            })
          }),
          new Promise((resolve, reject) => {
            // 猜你想问
            askingTask = wx.request({
              url: askingChunkedURL,
              method: 'post',
              // enableChunked: true, // 开启 transfer-encoding chunked
              data: {
                message,
                token: this.initData?.chat?.token,
                rid,
              },
              header: {
                Issuer: wxAppid,
                Authorization: store.userinfo?.token,
                _R_: 'ICHAT',
              },
              success: (res) => {
                // 这里是成功调用完的 可以写去掉加载中等逻辑
                // 由于SSE特性,需要由用户端断开连接,所以在使用完毕时,需要调用requestTask.abort()断开连接
                // askingTask.abort()

                resolve(res)
              },
              fail: (err) => {
                console.log('猜你想问接口错误', err)
                if (!err.errMsg === 'request:fail abort') {
                  uni.showToast({
                    title: '猜测问题获取失败',
                    icon: 'error',
                  })
                }
                reject(err)
              },
              complete: () => {
                this.askingChunkReceiving = false
                console.log(
                  'finally askingChunkReceiving:',
                  this.askingChunkReceiving
                )
              },
            })
          }),
        ])
          .then((result) => {
            console.log('Promise.all', result)
            const data = result[1].data?.data
            if (data?.length) {
              this.chatRecords.at(-1).question = data.map((item) =>
                item.replace(/<ref>.*?<\/ref>/, '')
              )
            }
          })
          .catch((err) => {
            console.log('Promise.all error', err)
          })

        let str2 = ''
        // 监听服务端返回的数据
        chatTask.onChunkReceived((res) => {
          // 小程序中不支持TextDecoder
          const decoder = new TextDecoder('utf-8')
          const text = decoder.decode(res.data)

          console.log(
            '%coriginal:%s',
            'color:#9254de;',
            text.replace(/\n/g, '↵')
          )

          if (/\[ER:\d+\]/.test(text)) {
            console.log('聊天异常:', text)
            this.messageSplicing('暂时无法回答您的问题')
            return
          }

          str2 += text
          // 替换前一条\n结尾和第二条data:开头,markdown语法包裹代码块的双反引号,单反引号暂不做处理，/```.+?```/gs 代码块
          str2 = str2
            .replace(/(\n\n)?data:|null|markdown|<[^>]*>|\[\d+\]/gis, '')
            // .replace(/<[^>]*>/g, '') // 移除html标签,html标签内可能包含其他内容,如</refdata:>
            // .replace(/\[\d+\]/g, '') // 如[1],该格式并非严格的markdown(格式化)链接语法
            // .replace(/!?\[([^\]]*)\]\(/g, '$1(') // markdown中的链接/图片
            // 代码块
            // .replace(/(`{3,})(.*?)\1/gm, '$2')
            // 用alt-text替换图像
            .replace(/\!\[(.*?)\][\[\(].*?[\]\)]/gs, '$1')
            // 内联链接
            .replace(/\[([^\]]*?)\][\[\(].*?[\]\)]/gs, '$2')
            // 粗体和斜体中的换行
            .replace(/(\*{1,3})(.*?)\1/gs, (_, asterisks, content) => {
              // 替换包裹内容中的换行符
              const cleanedContent = content.replace(/\n/g, '')
              return `${asterisks}${cleanedContent}${asterisks}`
            })

          let lastIndex = -1

          // 根据中文语法断句, MD语法断句
          if (str2.length > 30) {
            // 使用正则表达式匹配最后一个标点符号
            const regex =
              /(?:\d+\.[ ]|[-*+][ ]|#{1,6}[ ]|[-*]{3,}|(?<!\d)\.(?!\d)|[;!?；！？。,:，：、])/gm
            let match
            // 遍历找到最后一个匹配位置
            while ((match = regex.exec(str2)) !== null) {
              // console.log('match:', match)
              lastIndex = match.index
            }
            // \s 匹配任何不可见字符,包括空格、制表符、换页符等;等价于[ \f\n\r\t\v]; 只匹配空格可使用\x20或[ ]
            // \d+\.\s：匹配有序列表(数字加点后跟一个空格)
            // [-*+]\s：匹配无序列表(-、* 或 + 后跟一个空格)
            // #{1,6}\s：匹配标题(# 开头，支持 1 到 6 个 #)
            // [-*]{3,}：匹配水平线(3 个及以上的 - 或 *)
            // [.!?]：匹配常见标点符号 .、!、?
            // (?=[^]*$)：确保匹配的是最后一个符合条件的内容
            // (?<!\d)：负向前瞻，确保句号前面不是数字(排除小数点的前部分)
            // \.：匹配句号本身(只匹配英文句话的点)
            // (?!\d)：负向后瞻，确保句号后面不是数字(排除小数点的后部分) ## 部分浏览器后瞻断言要考虑兼容性

            console.log('split sentence:', lastIndex, match)
          }

          // 根据中文语法拼接/断句
          if (~lastIndex) {
            // str2.slice(0, lastIndex)>20
            const sentence = str2.slice(0, lastIndex) // lastIndex + 1 是包含标点符号的, 但匹配列表时不需要包含列表字符, 统一将标点放在后句句首
            console.log(
              '%cafter:',
              'color:#4096ff;',
              sentence.replace(/\n/g, '↵')
            )
            this.messageSplicing(sentence)
            str2 = str2.slice(lastIndex) // lastIndex + 1
          } else {
            if (~text.indexOf('[END]')) {
              console.log(
                '%cend after:',
                'color:#4096ff;',
                str2.replace(/\n/g, '↵')
              )
              str2 = str2.replace(/\[END\]/i, '')
              // 因为slice lastIndex取值, 末句可能出现只有标点的情况
              if (
                str2.replace(/(?<!\d)\.(?!\d)|[;!?；！？。,:，：、\n\s]/g, '')
                  .length
              ) {
                this.messageSplicing(str2)
                str2 = ''
              }
            }
          }
        })
      })
    }
  }
}
</script>

<style scoped>
.bg-mask {
  position: fixed;
  top: 44%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.phone-call-ripple {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  object-fit: cover;
  z-index: 99;
  pointer-events: none;
}

.chat-container {
  z-index: 10;
}

.bgPic {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}

.phone-call-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  /* background: url('/static/media/phoneMaskBg.png'); */
  /* background: url("../assets/chat_bg.png") no-repeat; */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 100;
}
</style>
