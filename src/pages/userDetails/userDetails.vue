<template>
  <view class="user-details">
    <up-row gutter="10">
      <up-col span="2">姓名</up-col>
      <up-col span="10">{{ simbotInfo.name }}</up-col>
    </up-row>
    <up-row gutter="10">
      <up-col span="2">公司</up-col>
      <up-col span="10">{{ simbotInfo.company }}</up-col>
    </up-row>
    <up-row gutter="10">
      <up-col span="2">职位</up-col>
      <up-col span="10">{{ simbotInfo.position }}</up-col>
    </up-row>
    <up-row gutter="10">
      <up-col span="2">电话</up-col>
      <up-col span="10">
        <up-copy :content="simbotInfo.mobile">
          <view class="d-flex align-items-baseline">
            <text class="me-3">{{ simbotInfo.mobile }}</text>
            <up-icon name="file-text" size="14"></up-icon>
          </view>
        </up-copy>
      </up-col>
    </up-row>
    <up-row gutter="10">
      <up-col span="2">行业</up-col>
      <up-col span="10">{{ simbotInfo.industry }}</up-col>
    </up-row>
    <up-row gutter="10">
      <up-col span="2">微信</up-col>
      <up-col span="10">{{ simbotInfo.wxNumber }}</up-col>
    </up-row>
    <up-row gutter="10">
      <up-col span="2">邮箱</up-col>
      <up-col span="10">{{ simbotInfo.email }}</up-col>
    </up-row>
    <up-row gutter="10">
      <up-col span="2">地址</up-col>
      <up-col span="10">{{ simbotInfo.address }}</up-col>
    </up-row>
  </view>
</template>
<script setup>
  import { reactive } from 'vue';
  import { onLoad, onShow } from '@dcloudio/uni-app';
  import { getSimbotInfo } from '@/api'
  const simbotInfo = reactive({})

  onShow(() => {
    uni.hideHomeButton()
  })
  onLoad((option) => {
    getSimbotInfo({
      data: {
        simbotId: option.simbotId
      }
    }).then(res => {
      Object.assign(simbotInfo, res.data)
    }).catch(err => {
      console.log('资料获取错误:', err)
      uni.showToast({
        title: '资料获取错误',
        icon: 'error'
      })
    })
  });
</script>
<style lang="scss" scoped>
  .user-details {
    margin: 24rpx;
    padding: 60rpx 40rpx;
    background-color: #333;
    color: #999;
    font-size: 30rpx;

    ::v-deep {
      .u-row {
        margin-bottom: 20rpx;

        &>view:first-child {
          align-self: flex-start;
          color: #666;
        }
      }
    }
  }
</style>