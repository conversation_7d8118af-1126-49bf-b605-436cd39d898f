<template>
  <view class="h-screen max-h-screen overflow-y-auto flex flex-col relative bg-[#24282F]">
    <up-navbar
    id="nav"
      :border="false"
      placeholder
      bgColor="transparent"
      :title="simbotInfo.name"
      titleWidth="240rpx"
    titleStyle="color:#fff"
    >
      <template #left>
        <up-icon
          v-if="showBack"
          name="arrow-left"
          size="20"
          @click="navigateBack"
        ></up-icon>
        <view v-else class="flex">
          <up-button
            class="!h-[52rpx] !px-0 !border-0 !w-auto !min-w-[52rpx] mr-1.5"
            size="small"
            shape="circle"
            @click="navigateTo('/pagesA/myCenter/myCenter')"
          >
            <!-- <up-icon name="home" size="16"></up-icon> -->
            <image
              class="w-3.5 h-3.5"
              src="/static/media/myCenter.png"
              mode="scaleToFill"
            />
          </up-button>
          <up-button
            class="!h-[52rpx] !w-auto !border-0"
            size="small"
            shape="circle"
            @click="navigateToHome"
          >
            <up-icon
              class="mr-0.5"
              name="users"
              custom-prefix="iconfont"
              size="16"
            ></up-icon>
            联系人
          </up-button>
        </view>
      </template>
      <template #right>
        <up-button
          v-show="initData.chat"
          class="!h-[52rpx] !min-w-0 !w-[52rpx] !border-0"
          :style="{ marginRight: menuButtonWidth + 'px' }"
          size="small"
          shape="circle"
          @click="sharePosterHandle"
        >
          <!-- open-type="share" -->
          <up-icon name="share" custom-prefix="iconfont" size="18"></up-icon>
        </up-button>
      </template>
    </up-navbar>
    
    <!-- 数字人视频背景 -->
    <view 
      id="media"
      class="fixed left-0 w-[100%] h-[70vh] [margin-top:calc(10%+128rpx)] overflow-hidden rotate-0 z-0 [mask-image:linear-gradient(to_bottom,white_0%,white_85%,transparent_100%)] [-webkit-mask-image:linear-gradient(to_bottom,white_0%,white_85%,transparent_100%)]"
    >
      <!-- rotate-0/transform:rotate(0deg)部分ios在子元素使用transform时造成border-radius失效问题 -->
      <template v-if="initData?.vivid?.image?.url">
        <video
          class=" w-[100vw] h-[180vw] transition-opacity opacity-0 duration-700"
          :class="{
            'opacity-100': mediaLoaded,
            'no-talk': !isTalk,
          }"
          object-fit="cover"
          :src="initData?.vivid?.talk?.url"
          :controls="false"
          :autoplay="true"
          :muted="true"
                disablePictureInPicture
          style="pointer-events: none;"
          loop

        ></video>
        <video
          class=" w-[100vw] h-[180vw] transition-opacity opacity-0 duration-500"
          :class="{ 'opacity-100': mediaLoaded }"
          object-fit="cover"
          :src="initData?.vivid?.silent?.url"
          :controls="false"
          :autoplay="true"
                     disablePictureInPicture
          style="pointer-events: none;"
          :muted="true"
          loop
          @loadedmetadata="onLoadedMetadata"
        ></video>
      </template>
      <template v-else>
        <view class="mt-[10%] text-center text-white">暂无数字人</view>
      </template>
      <!-- 音量控制按钮 -->
      <up-button
        class="!h-7 !min-w-0 !w-7 !border-0 !absolute top-2 right-2 z-30"
        size="small"
        shape="circle"
        @click="playOrPause"
      >
        <up-icon
          :name="soundOff ? 'volume-off' : 'volume'"
          size="20"
        ></up-icon>
      </up-button>
    </view>
    
    <!-- 内容层 - 设置指针事件穿透，不占用空间 -->
    <view id="content" class="relative z-20">
      <!-- mask 遮罩 -->
      <image 
        id="mask" 
        class="fixed bottom-0 z-10 w-screen h-[580rpx]" 
        src="/static/media/phonecallContentBlack.png" 
        mode="scaleToFill"
      />
      
      <!-- 聊天容器 -->
      <view class="fixed left-0 right-0 mx-3 mb-3 z-20 rounded-xl bottom-[40rpx] ">
        <!-- 聊天记录区域 -->
        <view class="text-sm flex-1 relative">
          <!-- 当scroll-view渲染先于recordsHeight获得高度值会造成下拉刷新失效 -->
          <scroll-view
            v-if="recordsHeight"
            id="scrollView"
            scroll-y
            :scroll-into-view="scrollToViewId"
            :refresher-enabled="hasMoreHistory"
            :refresher-triggered="chatHistoryPending"
            @refresherrefresh="getChatHistory"
            :scroll-with-animation="scrollWithAnimation"
            scroll-anchoring
            :scroll-top="scrollTop"
            class="[mask-image:linear-gradient(to_bottom,transparent_0%,white_8%,white_92%,transparent_100%)] [overflowAnchor:auto] max-h-[320rpx] mb-[20rpx] [-webkit-mask-image:linear-gradient(to_bottom,transparent_0%,white_8%,white_92%,transparent_100%)] relative"
          >

          <view class="text-center -mt-12 text-gray-500" v-if="!hasMoreHistory">
            没有更多了...</view
          >
          <template v-for="(item, index) in chatRecords" :key="item.id">
            <!-- <view v-if="shouldDisplayTime(index)"
              class="text-center text-slate-400 pt-4 mb-4">{{
                formatRelativeTime(item.dateTime) }}
            </view> --><!-- 影响滚动高度,设置上边距会影响高度 -->
            <!-- 以一组问答结尾 -->
            <view
              v-if="
                index === chatRecords.length - 2 &&
                item.type === 'question' &&
                item.type !== chatRecords.at(-1)?.type
              "

            >
              <view
                v-if="shouldDisplayTime(index)"
                class="text-center text-slate-400 mb-4"
                >{{ formatRelativeTime(item.dateTime) }}
              </view>
              <view
                v-if="item.type === 'question'"
                class="w-full flex mb-2.5 animate-fade-in"
                :id="item.id"
              >
                <view
                  class=" rounded-lg bg-[#4E677A] ml-auto py-2 px-2.5 text-white font-thin"
                  @longpress="copyMessage(item.content)"
                >
                  {{ item.content }}
                </view>
              </view>
              <view
                v-if="chatRecords[index + 1]?.type === 'answer'"
                class="w-full flex mb-2.5 animate-fade-in"
                :id="item.id"
              >
                <view
                id="answer"
                  class=" rounded-lg bg-[#2B363D] mr-auto py-2 px-2.5 text-white font-thin"
                  @longpress="copyMessage(chatRecords[index + 1].content)"
                >
                  <image
                    v-if="!chatRecords[index + 1].content"
                    class="w-[50rpx] h-3"
                    src="../../static/media/suspension.gif"
                    mode="aspectFit"
                  />
                  <template v-else>
                    <uamarkdown
                      :source="
                        chatRecords[index + 1].reply ||
                        chatRecords[index + 1].content
                      "
                      :showLine="false"
                    />
                    <view
                      class="bg-indigo-50 text-blue-500 px-2.5 py-1 rounded-lg inline-block mt-2"
                      v-if="chatRecords[index + 1].source"
                    >
                      回答来源: 苍南农商银行
                    </view>
                    <view
                      class="pt-4 mt-2 border-t border-indigo-100"
                      v-if="chatRecords[index + 1].question?.length"
                    >
                      <view class="mb-2.5 font-medium text-shadow-highlight">
                        猜你想问
                      </view>
                      <view
                        class="leading-5"
                        :class="{ 'mt-1': index > 0 }"
                        v-for="(subitem, index) in chatRecords[index + 1]
                          .question"
                        :key="subitem"
                      >
                        <text @click="sendText(subitem)"> {{ subitem }}</text>
                      </view>
                    </view>
                  </template>
                </view>
              </view>
            </view>
            <!-- 第一条欢迎语|连续多条问问/答(欢迎语为最后一条数据时获取历史聊天信息)结束 -->
            <view
              v-else-if="
                index === chatRecords.length - 1 &&
                (item.type === chatRecords[index - 1]?.type ||
                  !chatRecords[index - 1]?.type)
              "

            >
              <view
                v-if="shouldDisplayTime(index)"
                class="text-center text-slate-400 pt-4 mb-4"
                >{{ formatRelativeTime(item.dateTime) }}
              </view>
              <view class="w-full flex mb-2.5 animate-fade-in" :id="item.id">
                <view
                  class=" rounded-lg py-2 px-2.5 text-white font-thin"
                  :class="[
                    item.type === 'question'
                      ? 'bg-[#4E677A] ml-auto'
                      : 'bg-[#2B363D] mr-auto',
                  ]"
                  @longpress="copyMessage(item.content)"
                >
                  {{ item.content }}
                </view>
              </view>
            </view>
            <!-- 不是最后一组问/答(chatRecords.slice(index + 1).length>1进入页面只有欢迎语时直接获取历史末尾问题后相当于两条回答,如果只判断问题后是否还有问题,会认为是最后一组对话,跳过该问题)-->
            <template
              v-else-if="
                (item.type === 'question' &&
                  (chatRecords.slice(index + 1).length > 1 ||
                    chatRecords
                      .slice(index + 1)
                      .some((sliceItem) => sliceItem.type === 'question'))) ||
                (item.type === 'answer' &&
                  chatRecords
                    .slice(index + 1)
                    .some((sliceItem) => sliceItem.type === 'answer'))
              "
            >
              <view
                v-if="shouldDisplayTime(index)"
                class="text-center text-slate-400 pt-4 mb-4"
                >{{ formatRelativeTime(item.dateTime) }}
              </view>
              <view class="w-full flex mb-2.5 animate-fade-in" :id="item.id">
                <view
                  class=" rounded-lg py-2 px-2.5 text-white font-thin"
                  :class="[
                    item.type === 'question'
                      ? 'bg-[#4E677A] ml-auto'
                      : 'bg-[#2B363D] mr-auto',
                  ]"
                  @longpress="copyMessage(item.content)"
                >
                  <template v-if="item.type === 'question'">
                    {{ item.content }}
                  </template>
                  <template v-else>
                    <image
                      v-if="!item.content"
                      class="w-[50rpx] h-3"
                      src="../../static/media/suspension.gif"
                      mode="aspectFit"
                    />
                    <template v-else>
                      <uamarkdown
                        :source="item.reply || item.content"
                        :showLine="false"
                      />
                      <view
                        class="bg-indigo-50 text-blue-500 px-2.5 py-1 rounded-lg inline-block mt-2"
                        v-if="item.source"
                      >
                        回答来源: 苍南农商银行
                      </view>
                      <view
                        class="pt-4 mt-2 border-t border-indigo-100"
                        v-if="item.question?.length"
                      >
                        <view class="mb-2.5 font-medium text-shadow-highlight">
                          猜你想问
                        </view>
                        <view
                          class="leading-5"
                          :class="{ 'mt-1': index > 0 }"
                          v-for="(item, index) in item.question"
                          :key="item"
                        >
                          <text @click="sendText(item)"> {{ item }}</text>
                        </view>
                      </view>
                    </template>
                  </template>
                </view>
              </view>
            </template>

            <!-- 用于滚动到底部的占位元素,没有文字设置decode也不能显示空格 -->
            <!-- <text id="bottomAnchor" class="block invisible h-6">#bottom</text> -->
          </template>
        </scroll-view>
        
        <!-- 输入框区域 -->
        <view :style="[inputFocused?{position:'fixed',left:'-1rpx',bottom:`${keyboardHeight}px`,padding:'20rpx 30rpx',width:'100%'}:{}]">
        <view
          v-if="!speechInput"
          class="flex items-center p-[8rpx] rounded-full transition-colors duration-200 h-[76rpx]"
          :class="inputFocused ? 'bg-[#181A1D]' : 'bg-[#181A1D]'"
        >
          <image
            class="w-[60rpx] h-[60rpx] mr-2 bg-black rounded-full"
            v-show="!touched"
            src="/static/media/input_left_icon.png"
            mode="aspectFit"
            @click.stop="speechInput = !speechInput"
          />
          <input
            class="flex-1 text-sm px-2 text-white"
            :adjust-position="false"
            type="text"
            v-model.trim="inputValue"
            placeholder="说点什么..."
            confirm-type="send"
            placeholder-style="color: #DADADA;"
            @confirm="(e) => sendText(e.detail.value)"
          />
          <view
            v-if="inputValue.length"
            class="flex items-center justify-center w-[60rpx] h-[60rpx] rounded-full bg-gradient-to-t from-blue-500 to-cyan-300 transition-colors duration-200"
            @click.stop="() => sendText(inputValue)"
          >
            <up-icon
              name="send2"
              custom-prefix="iconfont"
              color="#ffffff"
              size="20"
            ></up-icon>
          </view>
          <image
            v-else
            src="/static/media/phone_right.png"
            class="w-[60rpx] h-[60rpx] block cursor-pointer bg-black rounded-full"
            @click.stop="navigateToPhoneCall"
          />
        </view>
        <view
          v-show="speechInput"
          class="flex items-center p-[8rpx] rounded-full transition-colors duration-200 h-[76rpx]"
          :class="touched ? 'bg-[#333]' : 'bg-[#181A1D]'"
        >
          <image
            class="w-[60rpx] h-[60rpx] bg-black rounded-full"
            v-if="!touched && recordMoveInFlag"
            src="/static/media/inputLeftKeybord.png"
            mode="aspectFit"
            @click.stop="speechInput = !speechInput"
          />
          <!-- getRecordRecognitionManager不能使用v-if会造成录音识别失效 -->
          <SpeechRecognition
            class="flex-1 h-[60rpx] flex items-center justify-center"
            @touchStart="recordTouchStart"
            @touchEnd="touched = false;inputFocused = true"
            @onStop="recordRecoStop"
            @moveIn ="recordMoveIn"
          >
          </SpeechRecognition>
          <image
            v-if="!touched && recordMoveInFlag"
            src="/static/media/phone_right.png"
            class="w-[60rpx] h-[60rpx] block cursor-pointer bg-black rounded-full"
            @click.stop="navigateToPhoneCall"
          />
        </view>
        </view>
      </view>
    </view>
    <canvas
      class="absolute w-[300px] h-[240px] -left-full"
      canvas-id="shareCanvas"
      id="shareCanvas"
    ></canvas>
    <canvas
      class="absolute w-[624px] h-[920px] right-full"
      canvas-id="posterCanvas"
      id="posterCanvas"
    ></canvas>
    <LoginModal></LoginModal>
    <up-popup
      class="!flex-none"
      :show="showShare"
      @close="showShare = false"
      bgColor="transparent"
    >
      <view class="flex flex-col" :style="{ height: shareHeight + 'px' }">
        <view class="flex-1 flex items-center justify-center">
          <view class="relative w-[624rpx] h-[920rpx]">
            <image
              class="w-full h-full"
              :src="posterTempPath"
              mode="scaleToFill"
            />
            <!-- <image class="absolute w-full h-full"
              src="../../static/media/share-bg.png" mode="scaleToFill" />
            <view class="absolute w-full flex flex-col items-center pt-24">
              <view class="w-20 h-20 overflow-hidden rounded-full mb-2.5">
                <image class="w-20" :src="initData.vivid?.image?.url"
                  mode="widthFix" />
              </view>
              <view class="text-white text-xl mb-1 max-w-48 truncate">{{
                simbotInfo.name }}</view>
              <view class="text-white text-sm mb-5 max-w-48 text-center">{{
                simbotInfo.company }}-{{
    simbotInfo.position }}</view>
              <view class="p-2 bg-[#F1F6FF] rounded-lg mb-5">
                <image class="w-24 h-24 block" :src="'data:image/jpeg;base64,'+shareQrcode"
                  mode="scaleToFill" />
              </view>
              <view class="text-sm">微信识别二维码7*24h为您服务</view>
            </view> -->
          </view>
        </view>
        <view class="py-5 bg-white">
          <view class="flex justify-around mb-5">
            <button
              class="after:content-none mx-0 px-0 bg-transparent text-sm leading-none"
              open-type="share"
            >
              <view
                class="w-12 h-12 flex justify-center items-center bg-[#50B674] rounded-full m-auto mb-2"
              >
                <up-icon name="weixin-fill" :size="36" color="#fff"></up-icon>
              </view>
              <text class="text-sm">微信好友</text>
            </button>
            <view @click="savePoster">
              <view
                class="w-12 h-12 flex justify-center items-center bg-[#1F5DE5] rounded-full m-auto mb-2"
              >
                <up-icon name="photo-fill" :size="36" color="#fff"></up-icon>
              </view>
              <text class="text-sm">保存到相册</text>
            </view>
          </view>
          <up-button class="!border-0 !text-base" @click="showShare = false"
            >取消</up-button
          >
        </view>
      </view>
    </up-popup>
  </view>
  </view>
</template>

<script>
  import { dateFormat, uid, isUndefined, removeMd } from '@/utils'
  import dayjs from 'dayjs'
  import {
    getChatInit,
    chatMessageHistory,
    // aiChat,
    aiChatURL,
    getSimbotInfo,
    askingChunkedURL,
    sharesCount,
    initPerson,
    sessionView,
    getShareQrcode,
  } from '@/api'
  // markdown格式解析
  import uamarkdown from '@/components/ua-markdown/ua-markdown.vue'
  import { useStore } from '@/stores/mainStore'
  import SpeechRecognition from '@/components/SpeechRecognition.vue'
  import { TextDecoder } from '@kayahr/text-encoding'
  import { wxAppid } from '@/assets/config'
  import { request } from '@/utils/request'
  import LoginModal from '@/components/LoginModal.vue'
  import AudioPlayer from '../../components/AudioPlayer'
  import XFTextToSpeech from '../../components/XunFeiTextToSpeech'
  import PublicTextToSpeech from '../../components/PublicTextToSpeech'
  import CosyVoiceTextToSpeech from '../../components/CosyVoiceTextToSpeech'
  // import removeMd from 'remove-markdown' // 有bug,在utils中重写;测试文本输入:贷款->贷款类型
  // import { v4 as uuidv4 } from 'uuid' // 标准uuid不适用CosyVoice参数

  const app = getApp()
  const systemInfo = uni.$u.sys()
  const windowHeight = systemInfo.windowHeight
  const store = useStore()
  let innerAudioContext = null
  let chatTask = null
  let askingTask = null
  let personInitPromise = null

  let ws = null // cosyVoice websocket 实例
  let task_id = null // 每轮合成标识
  const fs = wx.getFileSystemManager()
  let synthesisQueue = [] // 每条合成语音二进制数据
  let unsynthesized = [] // 待合成语音文本
  let unsynthesizedTemp = [] // 发送文本消息,上次语音合成未完成(未结束)时,缓存需合成语音文本

  const player = new AudioPlayer()
  let tts = null
  
  export default {
    components: { uamarkdown, SpeechRecognition, LoginModal },
    // 好友分享
    onShareAppMessage() {
      this.shared = true
      const roundId = uid(12)
      sharesCount({
        // 1-点击分享，2-海报，3-复制链接，4-发送朋友圈
        data: { simbotId: this.simbotId, type: 1, roundId },
      })
        .then(() => {
          console.log('转发给朋友统计调用成功', roundId)
        })
        .catch((err) => {
          console.log('转发给朋友统计调用失败', err)
        })
      return {
        title: '24小时在线为您服务~',
        path: `/pages/aiChat/aiChat?simbotId=${this.simbotId}&roundId=${roundId}`,
        imageUrl: this.shareImageUrl || '/static/media/share-default.png',
      }

      // return new Promise((resolve, reject) => {
      //   wx.showLoading()
      //   sharesCount({
      //     // 1-点击分享，2-海报，3-复制链接，4-发送朋友圈
      //     data: { simbotId: this.simbotId, type: 1 }, timeout: 2900
      //   })
      //     .then((result) => {
      //       roundId = result.data
      //       console.log('好友分享统计调用成功');
      //       wx.hideLoading()
      //       resolve({
      //         title: '24小时在线为您服务~',
      //         path: `/pages/aiChat/aiChat?simbotId=${this.simbotId}&roundId=${roundId}`,
      //         imageUrl: this.shareImageUrl || '/static/media/share-default.png',
      //       })
      //     })
      //     .catch((err) => {
      //       console.log(err);
      //       reject(err)
      //       console.log('好友分享统计调用失败')
      //       uni.showToast({
      //         title: "分享失败,请重试",
      //         icon: 'none',
      //       })
      //     })
      // })
    },
    // 朋友圈转发
    onShareTimeline() {
      this.shared = true
      const roundId = uid(12)
      sharesCount({
        // 1-点击分享，2-海报，3-复制链接，4-发送朋友圈
        data: { simbotId: this.simbotId, type: 4, roundId },
      })
        .then(() => {
          console.log('分享到朋友圈统计调用成功', roundId)
        })
        .catch((err) => {
          console.log('分享到朋友圈统计调用失败', err)
        })
      return {
        title: '24小时在线为您服务~',
        query: `simbotId=${this.simbotId}&roundId=${roundId}`,
        imageUrl: this.shareImageUrl || '/static/media/share-default.png',
      }
    },
    data() {
      return {
        chatRecords: [],
        speechInput: false, // 默认语音输入
        inputValue: '', // 输入文本
        soundOff: true, // 语音播放关闭
        scrollToViewId: '', // 滚动到的视图ID
        mediaHeight: 200, // 根据cover规则视频显示的实际高度,初始容器高度
        // recordsHeight: 0, // 聊天记录容器高度,改为计算属性了
        mediaClientRect: { width: 200, height: 200 }, // 视频容器视口信息
        keyboardHeight: 0, // 键盘高度
        safeAreaHeight: Math.max(systemInfo.safeAreaInsets?.bottom, 20), // 安全区域高度
        shareImageUrl: '', // 分享图片地址
        chatHistoryPending: false, // 标记下拉加载聊天记录scrollView loading状态
        startTime: dayjs('1970').format('YYYY-MM-DD HH:mm:ss'), // 聊天历史开始时间 非必填
        endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 聊天历史结束时间 非必填
        chatHistoryPageNo: 1, // 聊天历史页码
        hasMoreHistory: true, // 是否还有更多聊天记录
        initData: {}, // 页面初始数据
        simbotId: 0, // 机器人id,分享/联系人列表进入
        extraData: {}, // 小程序打开附加参数,获取机器人id或判断是否半屏打开等
        // source: false, // 回复信息是否显示来源
        chunkReceiving: false, // 问题回复-分块接收中
        askingChunkReceiving: false, // 猜你想问-分块接收中
        soundOff: false, // 是否关闭语音播放
        speechSynthesisQueue: [], // 语音合成队列 | CosyVoice 已合成语音列表
        playing: false, // 是否正在播放语音
        audioQueue: [], // 合成音频的播放队列
        showBack: false, // 是否显示返回按钮
        simbotInfo: {}, // 机器人信息
        menuButtonWidth: app.globalData.menuButtonClientRect.width, // 导航栏默认胶囊按钮宽度
        touched: false, // 语音输入按下
        recordMoveInFlag: true, // 语音输入移动
        mediaLoaded: false, // 视频加载完成;视频加载完成前会显示瞬间的黑色
        fillHeight: 0, // 为新消息可以滚动到scroll-view顶部,填充额外元素高度
        scrollTop: 0, // 滚动条位置
        scrollWithAnimation: true, // 滚动动画,在获取聊天记录后定位信息位置时不需要动画控制
        shared: false, // 分享会触发onHide/onShow,会中断聊天,区分是分享还是其他操作将小程序置于后台(无法阻止按钮分享行为)
        showShare: false, // 分享面板
        shareHeight:
          windowHeight -
          (systemInfo.safeAreaInsets?.bottom || 0) -
          systemInfo.statusBarHeight, // - 44
        shareQrcode: '', // 分享二维码 ArrayBuffer
        posterTempPath: '', // 分享海报临时路径
        sharePosterRoundId: '', // 海报二维码统计参数
        inputFocused: false, // 输入框是否获得焦点
        isTalk: false, // 是否正在播报语音（控制视频显示）
      }
    },
    // Created
    onLoad(options) {
      this.extraData = app.globalData?.extraData
      // 联系人跳转聊天通过参数获取, 获取不到取进入程序序参数
      this.simbotId = Number(options.simbotId || this.extraData.simbotId) // 68

      // 分享访问统计
      const roundId = this.extraData.roundId
      if (roundId) {
        sessionView({
          data: { simbotId: this.simbotId, roundId },
        })
          .then(() => {
            console.log('分享访问统计成功', roundId)
          })
          .catch((err) => {
            console.log('分享访问统计错误', err)
            // uni.showToast({
            //   title: `${err.errno}:${err.errMsg}`,
            //   icon: 'none',
            // })
          })
      }

      console.log(
        'aiChat onLoad:',
        options,
        { ...this.extraData },
        { ...store.userinfo },
        app.globalData.scene
      )

      this.showBack =
        getCurrentPages().length > 1 ||
        (app.globalData.scene === 1037 && this.extraData.simbotId)

      // 实际使用中获得容器下边界坐标(bottom)和视口高度减去容器上边坐标+容器高度所得值不一致,相差X9px,未知产生原因
      uni
        .createSelectorQuery()
        .select('#media')
        .boundingClientRect((rect) => {
          this.mediaClientRect = rect
          // this.recordsHeight = windowHeight - rect.top - rect.height - 20 - this.keyboardHeight - this.safeAreaHeight - 48 - 10 // 20为media容器下边距;48为底部输入栏高度;10为records容器距输入栏下边距
        })
        .exec()

      // 监听键盘高度变化
      uni.onKeyboardHeightChange((res) => {
        console.log('onKeyboardHeightChange:', res)
        this.keyboardHeight = res.height // 键盘高度
        // this.recordsHeight = windowHeight - mediaClientRect.top - mediaClientRect.height - this.keyboardHeight - this.safeAreaHeight - 78
      })

      // 播放状态
      player.onPlay((res) => {
        this.playing = true
        this.isTalk = true
      })

      player.onStop(() => {
        this.playing = false
        this.isTalk = false
      })

      // App.vue 中登录完成
      app.globalData.loginPromise.finally(() => {
        // 进入aiChat, simbotId必须存在
        const { token, expires } = store.userinfo
        if (!token || (token && +new Date() > expires)) {
          // 需要登录
          return
        }

        Promise.allSettled([
          // 初始化数据
          getChatInit({
            data: {
              simbotId: this.simbotId,
            },
          }),
          // 机器人信息
          getSimbotInfo({
            data: {
              simbotId: this.simbotId,
            },
          }),
        ]).then((results) => {
          console.log('%cinitData:', 'color:#13c2c2;', results[0], results[1])
          if (results[0].status === 'fulfilled') {
            this.initData = results[0].value.data
            getApp().initData = this.initData;
            const { url, projectKey, token, code, platform } =
              this.initData.voice
            
            switch (platform) {
              // 讯飞语音合成接口鉴权, 服务端会对date进行时钟偏移检查(300s), 超出偏差的请求都将被拒绝 https://www.xfyun.cn/doc/tts/online_tts/API.html#%E6%8E%A5%E5%8F%A3%E8%B0%83%E7%94%A8%E6%B5%81%E7%A8%8B
              case 'XunFeiOneSentence':
                tts = new XFTextToSpeech(url, {
                  header: {
                    app_id: projectKey,
                    res_id: code,
                  },
                })

                // 每隔5分钟重新获取请求链接
                setInterval(() => {
                  getChatInit({
                    data: {
                      simbotId: this.simbotId,
                    },
                  })
                    .then((res) => {
                      tts.url = res.data.voice.url
                      // xftts.updateURL(res.data.voice.url)
                    })
                    .catch((err) => {
                      console.log('讯飞语音合成刷新URL失败:', err)
                    })
                }, 300000)
                break
              case 'AliyunCosyVoiceTrain':
                tts = new CosyVoiceTextToSpeech(url, {
                  appkey: projectKey,
                  token,
                  voice: code,
                })
                break
              // 公共声音/个性化声音
              // case 'AliyunVoicePublic':
              // case 'AliyunMeasureTrain':
              default:
                tts = new PublicTextToSpeech(url, {
                  appkey: projectKey,
                  token,
                  voice: code,
                })
            }

            tts.onSynthesisCompleted((filePath, text) => {
              player.addAudio(filePath)
            })

            // 欢迎语
            this.chatRecords = [
              {
                // type: 'greetings',
                type: 'answer',
                id: '_' + uid(),
                content: this.initData.welcome,
                // dateTime: dateFormat(new Date(), 'hh:mm:ss'),
                dateTime: new Date(),
              },
            ]

            // 问候语播放
            // let welcome = this.initData.welcome
            // let lastIndex = 0;
            // welcome.replace(/([,!?;，。！？；])\s*/g, (match, p1, offset) => {
            //   const message = welcome.slice(lastIndex, offset + p1.length)
            //   if (message.length > 8) {
            //     this.messageSplicing(message)
            //     lastIndex = offset + p1.length;
            //   }
            //   return match
            // });
            // // 添加剩余的文本部分, 如结尾没有标点
            // if (lastIndex < welcome.length) {
            //   this.messageSplicing(welcome.slice(lastIndex));
            // }

            // 大模型初始化
            personInitPromise = new Promise((resolve, reject) => {
              initPerson({
                data: {
                  token: this.initData.chat?.token,
                },
              })
                .then((err) => {
                  console.log('大模型初始化完成', err)
                  resolve()
                })
                .catch((err) => {
                  console.log('大模型初始化失败:', err)
                  uni.showToast({
                    icon: 'error',
                    title: '大模型初始化失败',
                  })
                  reject()
                })
            })
          } else {
            // rejected
            console.log('获取聊天初始化数据失败:', results[0])
            uni.showToast({
              icon: 'error',
              title: '聊天初始化失败',
            })
          }

          if (results[1].status === 'fulfilled') {
            this.simbotInfo = results[1].value.data
            // 绘制分享图片
            this.drawShareImage()
          } else {
            console.log('获取机器人信息失败:', results[1])
            uni.showToast({
              icon: 'error',
              title: '信息获取失败',
            })
          }
        })
      })
    },
    onUnload() {
      console.log('onUnload:', this.playing, this.chunkReceiving)
      this.clear()
      // 取消绑定,防止影响其它页面(页面redirectTo跳转或直接退出不触发onHide)
      try {
        wx.offCopyUrl()
      } catch (err) {}
    },
    onShow() {
      // 绑定分享参数(小程序菜单-复制链接)
      try {
        wx.onCopyUrl(() => {
          const roundId = uid(12)
          sharesCount({
            // 1-点击分享，2-海报，3-复制链接，4-发送朋友圈
            data: { simbotId: this.simbotId, type: 3, roundId },
          })
            .then(() => {
              console.log('复制链接统计调用成功')
            })
            .catch((err) => {
              console.log('复制链接统计调用失败', err)
              // uni.showToast({
              //   title: `${err.errno}:${err.errMsg}`,
              //   icon: 'none',
              // })
            })
          return { query: 'simbotId=' + this.simbotId + '&roundId=' + roundId }
        })
      } catch (err) {
        console.log('复制链接添加参数失败', err)
      }

      this.shared = false
    },
    onHide() {
      console.log('onHide:', this.playing, this.chunkReceiving)
      this.clear()
      // 取消绑定,防止影响其它页面
      try {
        wx.offCopyUrl()
      } catch (err) {}
    },
    // mounted() {},
    computed: {
      // 聊天信息容器高度 - 现在使用固定的底部容器
      recordsHeight() {
        // 底部容器是50vh，减去输入框和内边距的高度
        return windowHeight * 0.5 - 120 // 120px 为输入框和内边距预留空间
      },
    },
    watch: {},
    methods: {
      recordMoveIn(bool){
        this.recordMoveInFlag= bool[0]
        this.touched = bool[1]
        console.log(bool,'wadadawd');
      
      },
      // 时间格式化函数
      formatRelativeTime(dateTime) {
        const now = dayjs()
        const messageTime = dayjs(dateTime)
        // 判断是否是今天
        if (messageTime.isSame(now, 'day')) {
          return messageTime.format('HH:mm')
        }
        // 判断是否是昨天
        else if (messageTime.isSame(now.subtract(1, 'day'), 'day')) {
          return '昨天 ' + messageTime.format('HH:mm')
        }
        // 更早的时间
        else {
          return messageTime.format('MM-DD HH:mm')
        }
      },
      // 判断是否需要显示时间
      shouldDisplayTime(index) {
        if (index === 0) return true // 第一条记录总是显示时间

        // 当前记录和前一条记录的时间差超过6分钟
        if (
          dayjs(this.chatRecords[index].dateTime) -
            dayjs(this.chatRecords[index - 1].dateTime) >=
          6 * 60 * 1000
        ) {
          return true
        }

        return false
      },
      clear() {
        // if (this.chunkReceiving) { // 欢迎语读取中进出不涉及this.chunkReceiving
        //   app.globalData.requestTasks.removeAllByKey(getCurrentPages().at(-1).route)
        // }

        if (!this.shared) {
          chatTask?.abort()
          askingTask?.abort()
        }

        // 停止合成
        tts.stopSynthesis()

        this.playing = false
        this.isTalk = false
        synthesisQueue = []
        unsynthesized = []
        this.speechSynthesisQueue = []

        player.stop()
      },
      abortTasks() {
        const currentPage = getCurrentPages().at(-1)?.route
        const tasks = app.globalData.requestTasks?.getTasksByKey(currentPage)
        if (tasks?.size) {
          tasks.forEach((task) => {
            if (task && task.abort) {
              task.abort()
              app.globalData.requestTasks.removeTaskByKey(currentPage, task)
            }
          })
        }
      },
      // 视频加载完成
      onLoadedMetadata(event) {
        console.log('%cmedia loaded', 'color:#9254de;', event.detail)
        
        this.$nextTick(() => {
          this.mediaLoaded = true
        })
      },
      // 录音/识别开始
      recordTouchStart(cb) {
        this.inputFocused = false 
        if (this.chunkReceiving || this.askingChunkReceiving) {
          // uni.showToast({
          //   title: '请等待回答完成',
          //   icon: 'none',
          //   duration: 2000,
          // })
          cb(false)
          return
        }
        if (this.playing) {
          this.clear()
        }
        this.touched = true
        cb(true)
      },
      // 语音识别完成
      recordRecoStop(res) {
        this.sendMessageHandle(res.result.replace(/小何/g, '小禾'))
      },
      // 发消息
      sendMessageHandle(message) {
        this.chatRecords.push(
          {
            type: 'question',
            id: '_' + uid(), // id或者class不能以数字开头
            content: message,
            // dateTime: dateFormat(new Date(), 'hh:mm:ss'),
            dateTime: new Date(),
          },
          {
            type: 'answer',
            id: '_' + uid(),
            content: '',
            // dateTime: dateFormat(+new Date() + 1000, 'hh:mm:ss'),
            dateTime: new Date(),
          }
        )

        this.$nextTick(() => {
          // setTimeout(() => {
          this.scrollToViewId = this.chatRecords.at(-2).id
          // }, 120)
        })

        this.chunkReceiving = true
        // this.askingChunkReceiving = true // 一定先于聊天请求返回是否需要??
        console.log('chunkReceiving:', true)

        // 大模型初始化完成才可以发送消息
        personInitPromise.then(async () => {
          // 确保上一轮语音合成完成
          if (
            ['XunFeiOneSentence', 'AliyunCosyVoiceTrain'].includes(
              this.initData?.voice?.platform
            )
          ) {
            await tts.synthesizedPromise()
            console.log('all Synthesized')
          }

          // 欢迎语在合成中,发送消息需取消合成中所有请求,requestTask中无法取消具体请求
          // aiChat({
          //   data: {
          //     message,
          //     token: this.initData?.chat?.token,
          //     ref: true
          //   },
          //   enableChunked: true, // 开启 transfer-encoding chunked
          //   intercept: false,
          //   canAbort: true,
          // })
          //   .then(() => {
          //     // 这里是成功调用完的 可以写去掉加载中等逻辑
          //     // 由于SSE特性,需要由用户端断开连接,所以在使用完毕时,需要调用requestTask.abort()断开连接
          //     chatTask.abort()
          //   })
          //   .catch((err) => {
          //     console.log('聊天接口错误', err)
          //     uni.showToast({
          //       title: '消息获取失败',
          //       icon: 'error',
          //     })
          //   })
          //   .finally(() => {
          //     this.chunkReceiving = false
          //     console.log('finally chunkReceiving:', this.chunkReceiving)
          //   })

          // chatTask = app.globalData.requestTasks
          //   .getTasksByKey(getCurrentPages().at(-1).route)
          //   ?.values()
          //   ?.next()?.value

          if (!this.soundOff) {
            console.log('语音播报状态:' + this.soundOff)
            // 恢复合成/播放
            tts.startSynthesis()
            player.start()
          }

          const rid = uid(32)

          // 微信升级到指定版本后,不支持并发enableChunked请求
          Promise.all([
            new Promise((resolve, reject) => {
              chatTask = wx.request({
                url: aiChatURL,
                method: 'post',
                enableChunked: true, // 开启 transfer-encoding chunked
                data: {
                  message,
                  token: this.initData?.chat?.token,
                  ref: this.extraData.source === 'true' ? 1 : 0,
                  rid,
                },
                header: {
                  Issuer: wxAppid,
                  Authorization: store.userinfo?.token,
                  _R_: 'ICHAT',
                },
                success: (res) => {
                  // 这里是成功调用完的 可以写去掉加载中等逻辑
                  // 由于SSE特性,需要由用户端断开连接,所以在使用完毕时,需要调用requestTask.abort()断开连接
                  chatTask.abort()

                  // if (res.statusCode === 200) {}
                  resolve(res)
                },
                fail: (err) => {
                  console.log('聊天接口错误', err)
                  if (err.errMsg !== 'request:fail abort') {
                    uni.showToast({
                      title: '消息获取失败',
                      icon: 'error',
                    })
                  }
                  reject(err)
                },
                complete: () => {
                  this.chunkReceiving = false
                  console.log('finally chunkReceiving:', this.chunkReceiving)
                },
              })
            }),
            new Promise((resolve, reject) => {
              // 猜你想问
              askingTask = wx.request({
                url: askingChunkedURL,
                method: 'post',
                // enableChunked: true, // 开启 transfer-encoding chunked
                data: {
                  message,
                  token: this.initData?.chat?.token,
                  rid,
                },
                header: {
                  Issuer: wxAppid,
                  Authorization: store.userinfo?.token,
                  _R_: 'ICHAT',
                },
                success: (res) => {
                  // 这里是成功调用完的 可以写去掉加载中等逻辑
                  // 由于SSE特性,需要由用户端断开连接,所以在使用完毕时,需要调用requestTask.abort()断开连接
                  // askingTask.abort()

                  resolve(res)
                },
                fail: (err) => {
                  console.log('猜你想问接口错误', err)
                  if (!err.errMsg === 'request:fail abort') {
                    uni.showToast({
                      title: '猜测问题获取失败',
                      icon: 'error',
                    })
                  }
                  reject(err)
                },
                complete: () => {
                  this.askingChunkReceiving = false
                  console.log(
                    'finally askingChunkReceiving:',
                    this.askingChunkReceiving
                  )
                },
              })
            }),
          ])
            .then((result) => {
              console.log('Promise.all', result)
              const data = result[1].data?.data
              if (data?.length) {
                this.chatRecords.at(-1).question = data.map((item) =>
                  item.replace(/<ref>.*?<\/ref>/, '')
                )
              }
            })
            .catch((err) => {
              console.log('Promise.all error', err)
            })

          let str2 = ''
          // 监听服务端返回的数据
          chatTask.onChunkReceived((res) => {
            // 小程序中不支持TextDecoder
            const decoder = new TextDecoder('utf-8')
            const text = decoder.decode(res.data)

            console.log(
              '%coriginal:%s',
              'color:#9254de;',
              text.replace(/\n/g, '↵')
            )

            if (/\[ER:\d+\]/.test(text)) {
              console.log('聊天异常:', text)
              this.messageSplicing('暂时无法回答您的问题')
              return
            }

            str2 += text
            // 替换前一条\n结尾和第二条data:开头,markdown语法包裹代码块的双反引号,单反引号暂不做处理，/```.+?```/gs 代码块
            str2 = str2
              .replace(/(\n\n)?data:|null|markdown|<[^>]*>|\[\d+\]/gis, '')
              // .replace(/<[^>]*>/g, '') // 移除html标签,html标签内可能包含其他内容,如</refdata:>
              // .replace(/\[\d+\]/g, '') // 如[1],该格式并非严格的markdown(格式化)链接语法
              // .replace(/!?\[([^\]]*)\]\(/g, '$1(') // markdown中的链接/图片
              // 代码块
              // .replace(/(`{3,})(.*?)\1/gm, '$2')
              // 用alt-text替换图像
              .replace(/\!\[(.*?)\][\[\(].*?[\]\)]/gs, '$1')
              // 内联链接
              .replace(/\[([^\]]*?)\][\[\(].*?[\]\)]/gs, '$2')
              // 粗体和斜体中的换行
              .replace(/(\*{1,3})(.*?)\1/gs, (_, asterisks, content) => {
                // 替换包裹内容中的换行符
                const cleanedContent = content.replace(/\n/g, '')
                return `${asterisks}${cleanedContent}${asterisks}`
              })

            let lastIndex = -1

            // 根据中文语法断句, MD语法断句
            if (str2.length > 30) {
              // 使用正则表达式匹配最后一个标点符号
              const regex =
                /(?:\d+\.[ ]|[-*+][ ]|#{1,6}[ ]|[-*]{3,}|(?<!\d)\.(?!\d)|[;!?；！？。,:，：、])/gm
              let match
              // 遍历找到最后一个匹配位置
              while ((match = regex.exec(str2)) !== null) {
                // console.log('match:', match)
                lastIndex = match.index
              }
              // \s 匹配任何不可见字符,包括空格、制表符、换页符等;等价于[ \f\n\r\t\v]; 只匹配空格可使用\x20或[ ]
              // \d+\.\s：匹配有序列表(数字加点后跟一个空格)
              // [-*+]\s：匹配无序列表(-、* 或 + 后跟一个空格)
              // #{1,6}\s：匹配标题(# 开头，支持 1 到 6 个 #)
              // [-*]{3,}：匹配水平线(3 个及以上的 - 或 *)
              // [.!?]：匹配常见标点符号 .、!、?
              // (?=[^]*$)：确保匹配的是最后一个符合条件的内容
              // (?<!\d)：负向前瞻，确保句号前面不是数字(排除小数点的前部分)
              // \.：匹配句号本身(只匹配英文句话的点)
              // (?!\d)：负向后瞻，确保句号后面不是数字(排除小数点的后部分) ## 部分浏览器后瞻断言要考虑兼容性

              console.log('split sentence:', lastIndex, match)
            }

            // 根据中文语法拼接/断句
            if (~lastIndex) {
              // str2.slice(0, lastIndex)>20
              const sentence = str2.slice(0, lastIndex) // lastIndex + 1 是包含标点符号的, 但匹配列表时不需要包含列表字符, 统一将标点放在后句句首
              console.log(
                '%cafter:',
                'color:#4096ff;',
                sentence.replace(/\n/g, '↵')
              )
              this.messageSplicing(sentence)
              str2 = str2.slice(lastIndex) // lastIndex + 1
            } else {
              if (~text.indexOf('[END]')) {
                console.log(
                  '%cend after:',
                  'color:#4096ff;',
                  str2.replace(/\n/g, '↵')
                )
                str2 = str2.replace(/\[END\]/i, '')
                // 因为slice lastIndex取值, 末句可能出现只有标点的情况
                if (
                  str2.replace(/(?<!\d)\.(?!\d)|[;!?；！？。,:，：、\n\s]/g, '')
                    .length
                ) {
                  this.messageSplicing(str2)
                  str2 = ''
                }
              }
            }
          })
        })
      },
      // 回复chunked消息拼接 语音合成
      messageSplicing(message) {
        const lastRecord = this.chatRecords.at(-1)
        lastRecord.content += message

        // 未设置声音voice返回null, voice:{platform: AliyunCosyVoiceTrain|AliyunVoicePublic}
        const { voice } = this.initData
        if (voice && !this.soundOff) {
          const plainMsg = removeMd(message)
          console.log(
            'plainMsg:',
            plainMsg.replace(/\n/g, '↵'),
            plainMsg.length
          )
          tts.speechSynthesis(plainMsg)
        }
      },
      // 音频播放开启/关闭
      playOrPause() {
        // 如存在异步未合成完成, 不允许再次马上切换
        if (this.soundOff && (this.isSynthesisStarted || this.fileWriting)) {
          return
        }

        this.soundOff = !this.soundOff
        if (this.soundOff) {
          // 停止TTS合成
          if (tts) {
            tts.stopSynthesis()
          }
          player.stop()
          // 快速切换或未在播放状态onStop可能不会触发
          this.playing = false
          this.isTalk = false
        }
      },
      // 获取聊天记录
      getChatHistory() {
        this.chatHistoryPending = true

        chatMessageHistory({
          data: {
            simbotId: this.simbotId,
            startTime: this.startTime, // 非必填
            endTime: this.endTime, // 非必填
            size: 5,
            page: this.chatHistoryPageNo,
          },
        })
          .then((res) => {
            const parseData = res.data
              .reverse()
              .reduce((acc, { query, answer, infer }) => {
                console.log({ query, answer, infer })
                query && acc.push(query)
                answer && acc.push(answer)
                // acc.push(infer) // 猜你想问暂不处理
                console.log(acc)
                return acc
              }, [])

            console.log('parseData', parseData)

            this.chatRecords = parseData
              .map((item) => {
                const buildItem = {
                  ...item,
                  id: '_' + item.id, // 作为锚点不能以数字开头
                  // content,
                  // dateTime: dateFormat(
                  //   new Date(item.gmtChat), // dateFormat中因为兼容问题将‘-’替换为‘/’,返回时间格式替换后new Date()为无效日期, 这里先格式化
                  //   +new Date(new Date().toLocaleDateString()) -
                  //     new Date(item.gmtCreate) >
                  //     0
                  //     ? 'MM-dd hh:mm:ss'
                  //     : 'hh:mm:ss'
                  // ),
                  dateTime: item.gmtChat,
                  content: item.content,
                  // .replace(/<ref>.*?<\/ref>/gs, '')
                  // .replace(/!?\[([^\]]*)\]\(/g, '$1('), // 聊天记录<ref>标签中没有/n
                  type: item.type === 'Query' ? 'question' : 'answer',
                }
                // console.log(buildItem)
                return buildItem
              })
              .concat(this.chatRecords)

            // this.hasMoreHistory = this.chatHistoryPageNo < Math.ceil(res.page.total / res.page.size)
            this.hasMoreHistory = res.page.total > res.page.size // res.data.length > 0

            this.scrollWithAnimation = false
            if (parseData.length) {
              this.$nextTick(() => {
                // nextTick锚点滚动位置仍有错误,可能存在其他的异步行为影响了DOM更新,手动创建微任务来确保在正确的时间点获取位置信息
                setTimeout(() => {
                  uni
                    .createSelectorQuery()
                    .select('#_' + parseData.at(-1)?.id)
                    .boundingClientRect((rect) => {
                      // 获取不到元素rect为null
                      if (rect) {
                        this.scrollTop = rect.top - this.recordsHeight - 45 // -rect.height:显示本次获取的聊天记录末条开始吗,同scrollToViewId;45为scroll-view下拉刷新阈值,末条记录对齐方式,如果消息内容较多,高度超过scroll-view高度,显示不全有些奇怪
                      }

                      // this.scrollToViewId(res.data.at(-1).id) // 滚动至末条开始
                      setTimeout(() => {
                        this.scrollWithAnimation = true
                      }, 200)
                    })
                    .exec()
                }, 160) // scrollView下拉高度会影响内容高度,必须等待下拉动画高度恢复后执行滚动到该元素
              })
            }

            // if(this.hasMoreHistory)
            this.chatHistoryPageNo += 1
          })
          .catch((err) => {
            console.log('获取聊天记录错误:' + err)
            uni.showToast({
              title: '获取聊天记录错误',
              icon: 'error',
            })
          })
          .finally(() => {
            this.chatHistoryPending = false
          })
      },
      // 文字输入发送消息
      sendText(text) {
        console.log(text)
        if (!text) {
          return
        }

        if (this.chunkReceiving || this.askingChunkReceiving) {
          // uni.showToast({
          //   title: '请等待回答完成',
          //   icon: 'none',
          //   duration: 2000,
          // })
          return
        }

        this.clear()

        this.sendMessageHandle(text)
        this.inputValue = ''
      },
      // 消息长按复制
      copyMessage(content) {
        if (content) {
          uni.setClipboardData({ data: content })
        }
      },
      // 返回上一页(关闭当前页)
      navigateBack() {
        // 从B小程序进入
        if (app.globalData.scene === 1037) {
          uni.navigateBackMiniProgram({
            fail: (err) => {
              console.log('navigateBackMiniProgram fail', err)
            },
          })
        } else {
          uni.navigateBack() // { delta: 1 }
        }
      },
      navigateTo(url) {
        uni.navigateTo({ url })
      },
      // 重定向我的
      navigateToHome() {
        // 内嵌, 通过打开半屏小程序能力打开的小程序
        if (app.globalData.apiCategory === 'embedded') {
          wx.exitMiniProgram({
            success: () => {
              console.log('exitMiniProgram success')
            },
            fail: (err) => {
              console.log('exitMiniProgram fail:', err)
            },
          })
          return
        }
        uni.reLaunch({ url: '/pages/contacts/contacts' })
      },
      sharePosterHandle() {
        // if (!this.shareQrcode) {
        this.sharePosterRoundId = uid(10)
        console.log(this.simbotId, this.sharePosterRoundId);
        
        Promise.all([
          getShareQrcode({
            data: {
              path: 'pages/aiChat/aiChat',
              scene: `simbotId=${this.simbotId}&roundId=${this.sharePosterRoundId}`,
              envVersion: __wxConfig.envVersion,
            },
            responseType: 'arraybuffer',
            intercept: false,
            loading: true,
          }),
          // sharesCount({
          //   // 1-点击分享，2-海报，3-复制链接，4-发送朋友圈
          //   data: {
          //     simbotId: this.simbotId,
          //     type: 2,
          //     roundId: this.sharePosterRoundId,
          //   },
          // }),
        ])
          .then((results) => {
            this.shareQrcode = uni.arrayBufferToBase64(results[0])
            this.drawSharePoster()
            this.showShare = true
          })
          .catch(() => {
            uni.showToast({
              title: '分享调用失败',
              icon: 'error',
            })
            console.log('获取分享二维码|分享统计失败:', err)
          })
      },
      // 分享海报绘制
      drawSharePoster() {
        const ctx = uni.createCanvasContext('posterCanvas')
        // 定义 canvas 和圆的属性
        const canvasWidth = 624
        const canvasHeight = 920
        const avatarRadius = 80 // 头像圆的半径
        const { company, name, position } = this.simbotInfo
        const textMaxWidth = 400

        const avatarUrl = this.initData.vivid?.image?.url
        // 头像圆心坐标
        let centerX = canvasWidth / 2
        let centerY = 260 // 90 + R40

        if (avatarUrl) {
          // 绘制背景图片
          ctx.drawImage(
            '../../static/media/share-bg.png',
            0,
            0,
            canvasWidth,
            canvasHeight
          )

          // 加载圆形中的前景图片
          uni.getImageInfo({
            src: avatarUrl, // 替换为圆形前景图片路径
            success: (avatarInfo) => {
              const avatarWidth = avatarInfo.width
              const avatarHeight = avatarInfo.height

              // 计算缩放比例, 保持前景图片不变形
              const scale = Math.max(
                (2 * avatarRadius) / avatarWidth,
                (2 * avatarRadius) / avatarHeight
              )
              const scaledWidth = avatarWidth * scale
              const scaledHeight = avatarHeight * scale
              const verticalOffset = scaledHeight / 2 - avatarRadius // 向下偏移(展示规则:宽度缩放顶部对齐)
              // 定义图片居中偏移量
              const offsetX = centerX - scaledWidth / 2
              const offsetY = centerY - scaledHeight / 2 + verticalOffset

              // 裁剪为圆形区域
              ctx.save()
              ctx.beginPath()
              ctx.arc(centerX, centerY, avatarRadius, 0, Math.PI * 2)
              ctx.clip()

              // 绘制圆形中的前景图片
              ctx.drawImage(
                avatarInfo.path,
                offsetX,
                offsetY,
                scaledWidth,
                scaledHeight
              )
              // 还原状态并绘制
              ctx.restore()

              // 姓名
              ctx.setFontSize(40) // 设置字体大小
              ctx.setFillStyle('#fff') // 设置字体颜色
              // ctx.font = '20px Arial'
              // const nameWidth = ctx.measureText(name).width
              // ctx.fillText(
              //   name,
              //   (canvasWidth - nameWidth) / 2,
              //   200,
              //   textMaxWidth
              // ) // 文字默认以左下角定位
              ctx.textAlign = 'center'

              this.drawText(ctx, name, canvasWidth / 2, 400, textMaxWidth, 28)

              // 公司-职位
              ctx.setFontSize(28)
              ctx.setFillStyle('#fff')
              // ctx.fillText(
              //   // `${company}-${position}`,
              //   '微信识别二维码7*24h为您服务微信识别二维码7*24h为您服务',
              //   canvasWidth / 2,
              //   226,
              //   textMaxWidth
              // )
              this.drawText(
                ctx,
                `${company}-${position}`,
                canvasWidth / 2,
                452,
                textMaxWidth,
                32,
                true
              )

              // 提示语
              ctx.setFontSize(28)
              ctx.setFillStyle('#080642')
              ctx.fillText('微信识别二维码7*24h为您服务', canvasWidth / 2, 812)

              // 圆角矩形
              const roundedRectWidth = 232
              const roundedRectHeight = 232
              // 圆角的半径
              const cornerRadius = 12

              // 起始点，与矩形左上角对齐
              const x = (canvasWidth - roundedRectWidth) / 2
              const y = 512

              // 开始绘制路径
              ctx.beginPath()
              ctx.moveTo(x + cornerRadius, y)
              // 右上角的圆角
              ctx.lineTo(x + roundedRectWidth - cornerRadius, y)
              ctx.arcTo(
                x + roundedRectWidth,
                y,
                x + roundedRectWidth,
                y + cornerRadius,
                cornerRadius
              )
              // 右下角的圆角
              ctx.lineTo(
                x + roundedRectWidth,
                y + roundedRectHeight - cornerRadius
              )
              ctx.arcTo(
                x + roundedRectWidth,
                y + roundedRectHeight,
                x + roundedRectWidth - cornerRadius,
                y + roundedRectHeight,
                cornerRadius
              )
              // 左下角的圆角
              ctx.lineTo(x + cornerRadius, y + roundedRectHeight)
              ctx.arcTo(
                x,
                y + roundedRectHeight,
                x,
                y + roundedRectHeight - cornerRadius,
                cornerRadius
              )
              // 左上角的圆角
              ctx.lineTo(x, y + cornerRadius)
              ctx.arcTo(x, y, x + cornerRadius, y, cornerRadius)
              // 关闭路径
              ctx.closePath()

              ctx.fillStyle = '#F1F6FF'
              ctx.fill()

              // 二维码
              const qrcodeWidth = 200
              const qrcodeHeight = 200
              const fs = uni.getFileSystemManager()
              // fs.writeFile({
              //   filePath: `${wx.env.USER_DATA_PATH}/qrcode.jpeg`,
              //   data: this.shareQrcode,
              //   encoding: 'base64',
              //   success(res) {
              //     console.log('/////////',res)
              //   },
              //   fail(res) {
              //     console.error('///////////',res)
              //   }
              // })
              try {
                const qrcodeTempPath = `${wx.env.USER_DATA_PATH}/qrcode.png`
                const res = fs.writeFileSync(
                  qrcodeTempPath,
                  this.shareQrcode,
                  'base64'
                )
                // 同步写入成功, res没有返回
                ctx.drawImage(
                  qrcodeTempPath,
                  (canvasWidth - qrcodeWidth) / 2,
                  528,
                  qrcodeWidth,
                  qrcodeHeight
                )
              } catch (e) {
                console.error('二维码写入失败')
              }

              // 最后绘制 canvas
              ctx.draw(true, () => {
                uni.canvasToTempFilePath({
                  canvasId: 'posterCanvas',
                  width: 624,
                  height: 920,
                  destWidth: 624,
                  destHeight: 920,
                  // fileType: 'png',
                  success: (res) => {
                    console.log('海报绘制成功:', res.tempFilePath)
                    this.posterTempPath = res.tempFilePath
                  },
                  fail: (err) => {
                    console.log('画布生成海报错误:', err)
                  },
                })
              })
            },
            fail: (error) => {
              console.log('头像加载失败', error)
            },
          })
        }
      },
      // 文字换行或省略
      drawText(ctx, t, x, y, w, l, wrap) {
        //ctx：canvas的 2d 对象, t：绘制的文字, x,y:文字坐标, w：文字最大宽度, l:行间距
        let chr = t.split('')
        let temp = ''
        let row = []

        for (let a = 0; a < chr.length; a++) {
          if (
            ctx.measureText(temp).width < w &&
            ctx.measureText(temp + chr[a]).width <= w
          ) {
            temp += chr[a]
          } else {
            row.push(temp)
            if (!wrap) {
              row.splice(-1, 1, temp + '...')
              temp = ''
              break
            }
            temp = chr[a]
          }
        }
        if (temp) {
          row.push(temp)
        }
        for (let b = 0; b < row.length; b++) {
          ctx.fillText(row[b], x, y + b * l) //每行字体y坐标间隔20-就是行间距
        }
      },
      // 保存海报到相册
      savePoster() {
        // 获取麦克风授权
        uni.getSetting({
          success: (res) => {
            // console.log(
            //   'getSetting:',
            //   res.authSetting,
            //   res.authSetting['scope.record']
            // )
            // 未授权过
            if (isUndefined(res.authSetting['scope.writePhotosAlbum'])) {
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success: () => {
                  console.log('已授权保存到相册')
                  this.savePosterHandle()
                  // 同意授权
                },
                fail: (error) => {
                  console.log('未授权隐私权限或拒绝保存到相册', error)
                  // errno: 104, errMsg: "requirePrivacyAuthorize:fail privacy permission is not authorized"
                  this.refusalAuthHandle()
                },
              })
              // 授权被拒绝
            } else if (res.authSetting['scope.writePhotosAlbum'] === false) {
              uni.showModal({
                content: '需要授权保存到相册，是否前往设置页面？',
                success: ({ confirm, cancel }) => {
                  // {errMsg: "showModal:ok", cancel: false, confirm: true, content: null}
                  if (confirm) {
                    uni.openSetting({
                      success: (res2) => {
                        if (res2.authSetting['scope.writePhotosAlbum']) {
                          console.log('再次提示后已授权保存到相册')
                          this.savePosterHandle()
                        } else {
                          // 仍未操作授权-提示
                          this.refusalAuthHandle()
                        }
                      },
                    })
                  } /* else if (cancel) {// 点击取消} */ else {
                    // 不同意-提示
                    this.refusalAuthHandle()
                  }
                },
              })
            } else {
              console.log('已授权保存到相册')
              this.savePosterHandle()
            }
          },
        })
      },
      refusalAuthHandle(message) {
        uni.showToast({
          icon: 'none',
          title: message || '您拒绝了授权，无法保存到相册',
        })
      },
      savePosterHandle() {
        uni.showLoading({
          mask: true,
        })
        sharesCount({
          // 1-点击分享，2-海报，3-复制链接，4-发送朋友圈
          data: {
            simbotId: this.simbotId,
            type: 2,
            roundId: this.sharePosterRoundId,
          },
          // loading: true
        })
          .then(() => {
            console.log('保存海报统计调用成功', this.sharePosterRoundId)
            uni.saveImageToPhotosAlbum({
              filePath: this.posterTempPath,
              success: function () {
                uni.showToast({
                  title: '保存成功',
                  icon: 'success',
                })
              },
              fail: (err) => {
                uni.showToast({
                  title: '保存失败',
                  icon: 'error',
                })
                console.log('保存海报到相册失败:', err)
              },
            })
          })
          .catch((err) => {
            uni.showToast({
              title: '保存失败',
              icon: 'error',
            })
            console.log('保存海报统计调用失败', err)
          })
      },
      // 分享图片绘制
      // drawShareImage() {
      //   const { company, name, position } = this.simbotInfo
      //   const imageUrl = this.initData.vivid?.image?.url

      //   let lineHeight = 24 // 行高
      //   let x1 = 26 // 文字的起始 X 坐标
      //   let y1 = 30 // 文字的起始 Y 坐标,会根据每行进行更新

      //   let x2 = x1 + 30 // 文字的起始 X 坐标
      //   let y2 = 0 // 文字的起始 Y 坐标,会根据每行进行更新
      //   let y2_last = 0
      //   let x3 = x1 + 30 // 文字的起始 X 坐标
      //   let y3 = name.length * lineHeight // 文字的起始 Y 坐标,会根据每行进行更新

      //   // 获取图片信息
      //   if (imageUrl) {
      //     uni.getImageInfo({
      //       src: imageUrl,
      //       success: (res) => {
      //         // const imgWidth = res.width
      //         // const imgHeight = res.height
      //         let ctx = uni.createCanvasContext('shareCanvas', this)
      //         ctx.setFillStyle('#222629')
      //         ctx.fillRect(0, 0, 350, 280)
      //         ctx.drawImage(res.path, 0, 0, 580, 928, 150, 0, 175, 280)

      //         // 设置字体样式和大小
      //         ctx.setFontSize(18)
      //         ctx.setFillStyle('#fff') // 设置字体颜色

      //         // 逐行处理文字
      //         for (let i = 0; i < company.length; i++) {
      //           // 更新 Y 坐标,实现竖向排版
      //           y1 = i * lineHeight
      //           // 写字
      //           ctx.fillText(company[i], x1, y1 + lineHeight) // 注意这里加了行高,让文字垂直居中
      //         }

      //         // 逐行处理文字
      //         for (let i = 0; i < name.length; i++) {
      //           // 更新 Y 坐标,实现竖向排版
      //           y2 = i * lineHeight
      //           y2_last = y2 + lineHeight
      //           // 写字
      //           ctx.fillText(name[i], x2, y2 + lineHeight) // 注意这里加了行高,让文字垂直居中
      //         }

      //         ctx.setFontSize(16)
      //         ctx.setFillStyle('#fff') // 设置字体颜色
      //         // 逐行处理文字
      //         for (let i = 1; i <= position.length; i++) {
      //           // 更新 Y 坐标,实现竖向排版
      //           y3 = i * lineHeight
      //           // 写字
      //           ctx.fillText(position[i - 1], x3, y3 + lineHeight + y2_last) // 注意这里加了行高,让文字垂直居中
      //         }

      //         // 将 Canvas 上的内容导出为图片
      //         ctx.draw(false, () => {
      //           uni.canvasToTempFilePath(
      //             {
      //               canvasId: 'shareCanvas',
      //               width: 350,
      //               height: 280,
      //               destWidth: 350,
      //               destHeight: 280,
      //               success: (res) => {
      //                 console.log('分享图临时路径:', res.tempFilePath)
      //                 this.shareImageUrl = res.tempFilePath
      //               },
      //               fail: (err) => {
      //                 console.log('画布内容导出生成图片错误:', err)
      //               },
      //             },
      //             this
      //           )
      //         })
      //       },
      //     })
      //   }
      // },
      // 绘制整个 canvas
      drawShareImage() {
        const ctx = uni.createCanvasContext('shareCanvas')
        // 定义 canvas 和圆的属性
        const canvasWidth = 300
        const canvasHeight = 240
        const canvasRadius = 75 // 圆的半径
        const { company, name, position, mobile } = this.simbotInfo

        const imageUrl = this.initData.vivid?.image?.url
        // 设置圆的位置
        let centerX = 210 // 圆心 x 坐标
        let centerY = 120 // 圆心 y 坐标(直接设置你想要的位置)
        // 绘制背景图片
        if (imageUrl) {
          uni.getImageInfo({
            src: 'https://cdn.xingqi.work/img/nebula-meta-tinyapp-wx/share.bg.png', // 替换为背景图片路径
            success: (bgRes) => {
              ctx.drawImage(bgRes.path, 0, 0, canvasWidth, canvasHeight)

              // 加载圆形中的前景图片
              uni.getImageInfo({
                src: imageUrl, // 替换为圆形前景图片路径
                success: (fgRes) => {
                  const imgWidth = fgRes.width
                  const imgHeight = fgRes.height

                  // 计算缩放比例，保持前景图片不变形
                  const scale = Math.max(
                    (2 * canvasRadius) / imgWidth,
                    (2 * canvasRadius) / imgHeight
                  )
                  const scaledWidth = imgWidth * scale
                  const scaledHeight = imgHeight * scale
                  const verticalOffset = 50 // 向下偏移的像素值
                  // 定义图片居中偏移量
                  const offsetX = centerX - scaledWidth / 2
                  const offsetY = centerY - scaledHeight / 2 + verticalOffset

                  // 裁剪为圆形区域
                  ctx.save()
                  ctx.beginPath()
                  ctx.arc(centerX, centerY, canvasRadius, 0, Math.PI * 2)
                  ctx.clip()

                  // 绘制圆形中的前景图片
                  ctx.drawImage(
                    fgRes.path,
                    offsetX,
                    offsetY,
                    scaledWidth,
                    scaledHeight
                  )
                  // 还原状态并绘制
                  ctx.restore()
                  // // 绘制文字
                  const text3 = '信禾管家为您服务'
                  ctx.setFontSize(16) // 设置字体大小
                  ctx.setFillStyle('#080642') // 设置字体颜色
                  ctx.fillText(text3, 15, 30) // 绘制单个字符

                  // 职位
                  const text5 = '客户经理'
                  ctx.font = 'bold normal  14px sans-serif'
                  ctx.setFontSize(14) // 设置字体大小
                  ctx.setFillStyle('#080642') // 设置字体颜色
                  ctx.fillText(text5, 15, 130) // 绘制单个字符
                  uni.getImageInfo({
                    src: 'https://cdn.xingqi.work/img/nebula-meta-tinyapp-wx/phone-dark.png', // 替换为背景图片路径
                    success: (res) => {
                      console.log('电话图标加载成功:', res.path)
                      ctx.drawImage(res.path, 15, 192, 15, 15)
                    },
                    fail: (error) => {
                      console.log('电话图标加载失败', error)
                    },
                  })
                  // 号码
                  const text8 = mobile
                  ctx.setFontSize(14) // 设置字体大小
                  ctx.setFillStyle('#080642') // 设置字体颜色
                  ctx.fillText(text8, 35, 205) // 绘制单个字符
                  // 公司
                  const text6 = '苍南农商银行'
                  ctx.setFontSize(14) // 设置字体大小
                  ctx.setFillStyle('#080642') // 设置字体颜色
                  ctx.fillText(text6, 35, 226) // 绘制单个字符
                  // 姓名
                  const text7 = name
                  // ctx.setFontSize(28) // 设置字体大小
                  ctx.setFillStyle('#080642') // 设置字体颜色
                  ctx.font = 'normal bold 28px sans-serif'
                  ctx.fillText(text7, 15, 100) // 绘制单个字符
                  uni.getImageInfo({
                    src: 'https://cdn.xingqi.work/img/nebula-meta-tinyapp-wx/compan-dark.png',
                    success: (res) => {
                      console.log('企业图标加载成功:', res.path)
                      ctx.drawImage(res.path, 15, 215, 14, 13)
                      // 最后绘制 canvas
                      ctx.draw(true, () => {
                        uni.canvasToTempFilePath({
                          canvasId: 'shareCanvas',
                          width: 350,
                          height: 280,
                          destWidth: 350,
                          destHeight: 280,
                          success: (res) => {
                            console.log(
                              '----------分享图绘制成功----------',
                              res.tempFilePath
                            )
                            this.shareImageUrl = res.tempFilePath
                          },
                          fail: (err) => {
                            console.log('画布内容导出生成图片错误:', err)
                          },
                        })
                      })
                    },
                    fail: (error) => {
                      console.log('企业图标加载失败', error)
                    },
                  })
                },
                fail: (error) => {
                  console.log('前景图片加载失败', error)
                },
              })
            },
            fail: (error) => {
              console.log('背景图片加载失败', error)
            },
          })
        }
      },
      // 跳转到电话页面
      navigateToPhoneCall() {
        uni.navigateTo({
          // url: '/pages/phoneCall/phoneCall'
          url: `/pages/phoneCall/phoneCall?simbotId=${this.simbotId}`
        })
      },
    },
  }
</script>

<style scoped>
:deep(.ua__markdown) {
  color: white;
  font-weight: 100;
}

.no-talk {
position: absolute;
}
</style>

