/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // './index.html',
    './src/**/*.{vue,tsx}',  // 确保扫描这些文件以找到 TailwindCSS 的类
  ],
  defaultExtractor: content => content.match(/[\w-/:]+(?<!:)/g) || [],
  // important: true, // 全局添加 !important
  theme: {
    // override the default
    // textShadow: {
    //   'default': '0 2px 0 #000',
    //   'md': '0 2px 2px #000',
    // },
    extend: {
      // extend the default
      textShadow: {
        highlight: '0 0 black',
        // '2xl': '1px 1px 5px rgb(33 34 43 / 20%)',
        // '3xl': '0 0 3px rgba(0, 0, 0, .8), 0 0 5px rgba(0, 0, 0, .9)',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
      },
      animation: {
        'fade-in': 'fadeIn 1s ease-in-out',
      },
    },
  },
  plugins: [
    require('tailwindcss-textshadow')
  ],
  // mode: 'jit',
  corePlugins: {
    preflight: false, // 禁用preflight(重置)样式,避免与微信小程序的默认样式冲突(会造成border等样式使用方式不统一);weapp-tailwindcss会自动启用preflight注入默认样式
  },
}

